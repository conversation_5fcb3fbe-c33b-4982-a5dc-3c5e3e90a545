import os
import re
from tkinter import filedialog
from tkinter import Tk

def get_frontmatter_and_content(file_path):
    """
    Reads a file and separates YAML frontmatter from the main content.
    Returns a tuple: (frontmatter_dict, content_string, frontmatter_lines_count).
    Returns (None, original_content, 0) if no frontmatter is found.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return None, "", 0

    if not lines or not lines[0].strip() == "---":
        return None, "".join(lines), 0

    frontmatter_lines = []
    content_lines = []
    in_frontmatter = False
    frontmatter_closed = False
    frontmatter_line_count = 0

    for i, line in enumerate(lines):
        stripped_line = line.strip()
        if i == 0 and stripped_line == "---":
            in_frontmatter = True
            frontmatter_lines.append(line)
            frontmatter_line_count += 1
            continue
        
        if in_frontmatter:
            frontmatter_lines.append(line)
            frontmatter_line_count += 1
            if stripped_line == "---":
                in_frontmatter = False
                frontmatter_closed = True
            continue
        
        content_lines.append(line)

    if not frontmatter_closed and in_frontmatter: # Frontmatter started but not closed
        return None, "".join(lines), 0

    if not frontmatter_lines:
        return None, "".join(content_lines), 0

    fm_dict = {}
    # Basic parsing for title, avoiding full YAML dependency for this specific task
    for fm_line in frontmatter_lines[1:-1]: # Skip the --- delimiters
        if ":" in fm_line:
            key, *value_parts = fm_line.split(":", 1)
            key = key.strip()
            value = "".join(value_parts).strip()
            # Remove surrounding quotes if any, for simple string values
            if (value.startswith("'") and value.endswith("'")) or \
               (value.startswith('"') and value.endswith('"')):
                value = value[1:-1]
            fm_dict[key] = value
            
    return fm_dict, "".join(content_lines), frontmatter_line_count

def update_markdown_file(file_path, filename_title):
    """
    Updates the YAML frontmatter of a markdown file with a title.
    If a title already exists, it's not replaced.
    """
    frontmatter, content, fm_lines_count = get_frontmatter_and_content(file_path)
    
    made_change = False
    
    if frontmatter is not None:
        # Frontmatter exists, check for title
        if "title" not in frontmatter or not frontmatter["title"]:
            # Add title
            # Reconstruct frontmatter with new title
            new_frontmatter_lines = ["---\n"]
            title_added = False
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    original_lines = f.readlines()
            except Exception as e:
                print(f"Error re-reading file {file_path} for update: {e}")
                return

            existing_fm_lines = original_lines[:fm_lines_count-1] # up to the closing ---
            
            for line in existing_fm_lines[1:]: # Skip the opening ---
                if line.strip().startswith("title:"): # Should not happen based on check, but good for structure
                    new_frontmatter_lines.append(f"title: {filename_title}\n")
                    title_added = True
                else:
                    new_frontmatter_lines.append(line)
            
            if not title_added:
                 # Insert title, typically at the beginning of the frontmatter
                new_frontmatter_lines.insert(1, f"title: {filename_title}\n")

            new_frontmatter_lines.append("---\n")
            
            full_new_content = "".join(new_frontmatter_lines) + content
            made_change = True
            print(f"Updating title in: {file_path}")
        else:
            print(f"Title already exists in: {file_path} - Skipping.")
            return # No change needed
    else:
        # No frontmatter, create it
        new_frontmatter = f"---\ntitle: {filename_title}\n---\n"
        full_new_content = new_frontmatter + content
        made_change = True
        print(f"Adding new frontmatter with title to: {file_path}")

    if made_change:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(full_new_content)
        except Exception as e:
            print(f"Error writing updated file {file_path}: {e}")


def main():
    root = Tk()
    root.withdraw()  # Hide the main Tkinter window
    folder_path = filedialog.askdirectory(title="Select Folder with Markdown Files")

    if not folder_path:
        print("No folder selected. Exiting.")
        return

    print(f"Scanning folder: {folder_path}")

    for filename in os.listdir(folder_path):
        if filename.endswith(".md"):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path): # Ensure it's a file, not a directory ending in .md
                # Use filename without extension as title
                title_from_filename = os.path.splitext(filename)[0]
                update_markdown_file(file_path, title_from_filename)
    
    print("\nProcessing complete.")

if __name__ == "__main__":
    main()
