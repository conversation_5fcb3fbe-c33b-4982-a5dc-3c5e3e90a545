#!/usr/bin/env python3
"""
YouTube URL Extractor from HTML Clipboard
==========================================

This script extracts YouTube video links and titles from HTML content in the clipboard
and adds them to an InputURL.md file. It removes duplicates and allows the user to
browse for the target folder.

Features:
- Extracts YouTube video URLs and titles from HTML
- Supports both youtube.com/watch and youtu.be formats
- Removes duplicate URLs
- Allows folder browsing or uses hardcoded path
- Creates InputURL.md if it doesn't exist
- Preserves existing URLs in the file

Requirements:
- pyperclip: pip install pyperclip
- beautifulsoup4: pip install beautifulsoup4
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import re
import sys
from pathlib import Path
from urllib.parse import parse_qs, urlparse
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import pyperclip
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install pyperclip beautifulsoup4")
    sys.exit(1)


# Configuration
HARDCODED_FOLDER_PATH = r"C:\Users\<USER>\Documents\HEW Notes\Video Transcripts MD"
INPUT_URL_FILENAME = "InputURL.md"


def extract_video_id_from_url(url):
    """Extract YouTube video ID from various URL formats."""
    # Handle different YouTube URL formats
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]{11})',
        r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        r'youtube\.com/v/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None


def normalize_youtube_url(url):
    """Convert YouTube URL to standard format."""
    video_id = extract_video_id_from_url(url)
    if video_id:
        return f"https://www.youtube.com/watch?v={video_id}"
    return url


def extract_youtube_links_from_html(html_content):
    """Extract YouTube video links and titles from HTML content."""
    soup = BeautifulSoup(html_content, 'html.parser')
    video_data = []

    # Look specifically for playlist video renderers (the actual playlist items)
    playlist_videos = soup.find_all('ytd-playlist-video-renderer')

    for playlist_video in playlist_videos:
        # Find the video title link within the playlist video renderer
        title_link = playlist_video.find('a', {'id': 'video-title'})
        if title_link and title_link.get('href'):
            href = title_link.get('href')
            title = title_link.get_text(strip=True) or title_link.get('title', 'Untitled')

            # Convert relative URLs to absolute
            if href.startswith('/watch?'):
                full_url = f"https://www.youtube.com{href}"
            elif href.startswith('http'):
                full_url = href
            else:
                continue

            # Extract video ID and normalize URL
            video_id = extract_video_id_from_url(full_url)
            if video_id:
                normalized_url = normalize_youtube_url(full_url)
                video_data.append({
                    'url': normalized_url,
                    'title': title,
                    'video_id': video_id
                })

    # If no playlist videos found, fall back to looking for div elements with id="meta"
    # but only from ytd-playlist-video-renderer containers
    if not video_data:
        meta_divs = soup.find_all('div', {'id': 'meta'})

        for meta_div in meta_divs:
            # Check if this meta div is within a playlist video renderer
            parent_playlist = meta_div.find_parent('ytd-playlist-video-renderer')
            if not parent_playlist:
                continue  # Skip if not in a playlist video renderer

            # Find the video title link within the meta div
            title_link = meta_div.find('a', {'id': 'video-title'})
            if title_link and title_link.get('href'):
                href = title_link.get('href')
                title = title_link.get_text(strip=True) or title_link.get('title', 'Untitled')

                # Convert relative URLs to absolute
                if href.startswith('/watch?'):
                    full_url = f"https://www.youtube.com{href}"
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue

                # Extract video ID and normalize URL
                video_id = extract_video_id_from_url(full_url)
                if video_id:
                    normalized_url = normalize_youtube_url(full_url)
                    # Check if we already have this video
                    if not any(v['video_id'] == video_id for v in video_data):
                        video_data.append({
                            'url': normalized_url,
                            'title': title,
                            'video_id': video_id
                        })

    return video_data


def get_target_folder():
    """Get the target folder path, use hardcoded path if it exists."""
    # Use hardcoded path if it exists
    if os.path.exists(HARDCODED_FOLDER_PATH):
        return HARDCODED_FOLDER_PATH

    # Browse for folder if hardcoded path doesn't exist
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    folder_path = filedialog.askdirectory(
        title="Select folder for InputURL.md file",
        initialdir=os.getcwd()
    )

    if not folder_path:
        messagebox.showwarning("No Folder Selected", "No folder was selected. Exiting.")
        return None

    return folder_path


def read_existing_urls(file_path):
    """Read existing URLs from the InputURL.md file."""
    existing_urls = set()
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):  # Skip comments and empty lines
                        # Extract URL if line contains external markdown link format
                        if line.startswith('- [') and '](' in line and line.endswith(')'):
                            url = line.split('](')[1][:-1]
                        elif line.startswith('[') and '](' in line and line.endswith(')'):
                            url = line.split('](')[1][:-1]
                        else:
                            url = line

                        # Normalize the URL
                        normalized = normalize_youtube_url(url)
                        existing_urls.add(normalized)
        except Exception as e:
            print(f"Warning: Could not read existing file {file_path}: {e}")

    return existing_urls


def write_urls_to_file(file_path, video_data, existing_urls):
    """Write URLs to the InputURL.md file."""
    new_urls = []
    duplicate_count = 0

    # Filter out duplicates and skip "Untitled" videos (likely Shorts)
    for video in video_data:
        if video['url'] not in existing_urls:
            # Skip videos with generic "Untitled" titles (likely Shorts or hidden videos)
            if video['title'].strip().lower() in ['untitled', '']:
                print(f"Skipping untitled video: {video['url']}")
                continue
            new_urls.append(video)
        else:
            duplicate_count += 1

    if not new_urls:
        print(f"No new URLs to add. Found {duplicate_count} duplicates.")
        return 0

    # Append new URLs to file
    try:
        with open(file_path, 'a', encoding='utf-8') as f:
            if os.path.getsize(file_path) > 0:
                f.write('\n')  # Add newline if file is not empty

            for video in new_urls:
                # Write as external markdown link format
                f.write(f"- [{video['title']}]({video['url']})\n")

        print(f"Successfully added {len(new_urls)} new URLs to {file_path}")
        if duplicate_count > 0:
            print(f"Skipped {duplicate_count} duplicate URLs")
        return len(new_urls)

    except Exception as e:
        print(f"Error writing to file {file_path}: {e}")
        return 0


def main():
    """Main function to extract YouTube URLs from clipboard and save to file."""
    print("YouTube URL Extractor from HTML Clipboard")
    print("=" * 50)
    
    # Get HTML content from clipboard
    try:
        html_content = pyperclip.paste()
        if not html_content:
            print("Error: Clipboard is empty")
            return
        
        if '<html' not in html_content.lower() and '<div' not in html_content.lower():
            print("Warning: Clipboard content doesn't appear to be HTML")
            print("First 200 characters:")
            print(html_content[:200])
            proceed = input("\nProceed anyway? (y/n): ").lower().strip()
            if proceed != 'y':
                return
                
    except Exception as e:
        print(f"Error reading from clipboard: {e}")
        return
    
    # Extract YouTube links
    print("Extracting YouTube links from HTML...")
    video_data = extract_youtube_links_from_html(html_content)
    
    if not video_data:
        print("No YouTube video links found in the HTML content")
        return
    
    print(f"Found {len(video_data)} YouTube video(s):")
    for i, video in enumerate(video_data, 1):
        print(f"  {i}. {video['title'][:60]}{'...' if len(video['title']) > 60 else ''}")
        print(f"     {video['url']}")
    
    # Get target folder
    folder_path = get_target_folder()
    if not folder_path:
        return
    
    # Create full file path
    file_path = os.path.join(folder_path, INPUT_URL_FILENAME)
    
    # Read existing URLs
    existing_urls = read_existing_urls(file_path)
    print(f"\nFound {len(existing_urls)} existing URLs in {INPUT_URL_FILENAME}")
    
    # Write new URLs to file
    added_count = write_urls_to_file(file_path, video_data, existing_urls)
    
    if added_count > 0:
        print(f"\n✅ Successfully processed! Added {added_count} new URLs to:")
        print(f"   {file_path}")
    else:
        print(f"\n⚠️  No new URLs were added to {file_path}")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
