#!/usr/bin/env python3
"""
Bulk Text File Editor for Markdown Files - Generic Find and Replace

This script allows the user to browse to a folder and bulk edits all .md files found in only the top level of the folder.
It replaces all occurrences of a specified string with another specified string in each markdown file.
The strings to find and replace are hardcoded at the top of the script for easy modification.

Usage:
    python replace-summary-heading.py

The script will open a folder selection dialog for the user to choose the target folder.
"""

import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox

# --- Configuration: Set your find and replace strings here ---
# The string to find (can be a regex pattern)
FIND_STRING = r'^# Backlog$'
# The string to replace the found string with
REPLACE_STRING = '# ToDo'
# Set to True if FIND_STRING is a regex, False for literal string matching
USE_REGEX = True
# --- End Configuration ---


def process_file(file_path):
    """
    Process a markdown file to replace occurrences of FIND_STRING with REPLACE_STRING.

    Args:
        file_path (str): Path to the markdown file

    Returns:
        tuple: (bool, int) - Success status and count of replacements made
    """
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Perform find and replace
        if USE_REGEX:
            # Treat FIND_STRING as a regular expression
            modified_content, replacements = re.subn(FIND_STRING, REPLACE_STRING, content, flags=re.MULTILINE)
        else:
            # Treat FIND_STRING as a literal string
            # For literal string replacement, re.subn is still convenient for counting replacements
            # We need to escape any special regex characters in FIND_STRING if we were to use it directly in re.subn
            # A simpler way for literal replacement is str.replace, but it doesn't count replacements directly.
            # So, we'll stick to re.subn but ensure FIND_STRING is treated literally if not regex.
            # However, the current setup with USE_REGEX handles this. If not using regex,
            # the user should provide a literal string for FIND_STRING.
            # For clarity and directness if USE_REGEX is False:
            temp_content = content.replace(FIND_STRING, REPLACE_STRING)
            if temp_content != content:
                replacements = content.count(FIND_STRING) # Count occurrences of the literal string
                modified_content = temp_content
            else:
                replacements = 0
                modified_content = content


        # If changes were made, write back to the file
        if replacements > 0:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(modified_content)
            return True, replacements

        return False, 0

    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False, 0


def process_folder(folder_path):
    """
    Process all markdown files in the specified folder.

    Args:
        folder_path (str): Path to the folder containing markdown files

    Returns:
        tuple: (total_files, modified_files, total_replacements)
    """
    # Check if the folder exists
    if not os.path.isdir(folder_path):
        messagebox.showerror("Error", f"The folder '{folder_path}' does not exist.")
        return 0, 0, 0

    print(f"Processing markdown files in: {folder_path}")

    # Get all markdown files in the top level of the folder
    md_files = [f for f in os.listdir(folder_path) if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]

    if not md_files:
        print("No markdown files found in the specified folder.")
        return 0, 0, 0

    print(f"Found {len(md_files)} markdown files.")

    # Process each file
    total_files_modified = 0
    total_replacements = 0

    for file_name in md_files:
        file_path = os.path.join(folder_path, file_name)
        modified, replacements = process_file(file_path)

        if modified:
            total_files_modified += 1
            total_replacements += replacements
            print(f"Modified: {file_name} - Replaced {replacements} occurrences of '{FIND_STRING}' with '{REPLACE_STRING}'")

    return len(md_files), total_files_modified, total_replacements


def main():
    """Main function to browse for a folder and process markdown files."""
    # Create a root window but hide it
    root = tk.Tk()
    root.withdraw()

    # Show folder selection dialog
    folder_path = filedialog.askdirectory(
        title="Select Folder with Markdown Files",
        mustexist=True
    )

    # If user cancels the dialog
    if not folder_path:
        print("Folder selection cancelled.")
        return

    # Process the selected folder
    total_files, modified_files, total_replacements = process_folder(folder_path)

    # Print summary
    if total_files > 0:
        print("\nSummary:")
        print(f"Total files processed: {total_files}")
        print(f"Files modified: {modified_files}")
        print(f"Total replacements made: {total_replacements}")

        # Show a message box with the summary
        messagebox.showinfo(
            "Processing Complete",
            f"Total files processed: {total_files}\n"
            f"Files modified: {modified_files}\n"
            f"Total replacements made: {total_replacements}"
        )


if __name__ == "__main__":
    main()
