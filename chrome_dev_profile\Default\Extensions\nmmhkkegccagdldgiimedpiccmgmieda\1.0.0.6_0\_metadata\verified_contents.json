[{"description": "treehash per file", "signed_content": {"payload": "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", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "WCuncRbp6bJZa7G4zE4_R32TbVvoB3M5tEJqM7eWSKdaCI6Z_y3xVtNhsVOguOaKwVFWWo-dwUn65Pg_uetB3MCKeyQiJ5MWasqBO8qkpXcbBW9VpKAYPJ2xVFiNuCRcLP9Brc595heiJ5Xm0Zjm3HsvLH5woub_M3ep9rMQWns"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "CRlWl0sZjmKbqZFXXgnWN2cgwABjeCB-9_GrpgDl40Fk5AO6SIf1kPLSKqHLcwf-NrvtM6rovN0La9d8Ia2WVq4BTOMnQszvVH62rWIZpYgtkNLNCBIz3EhqUZSF-ER8eUb_QxluKaJj92q8iL6g3ZwJD_McQT4D5Wiyokz7je6OQf__Q0JqQOmTJuPiFqA_orrmoenrVC_Tmrb70bUcE66UlSTw4j_kDKT5WKcTm_HzS53VZo43itgo7le_5k9EL8eFYOY5PHUSu3oFJkfbAoAYDuTlQMWeFgMQhnvbJdyiFWn18JjOs49LbUAMfZ2QotGUWOaD4HOQLxlqxEk7qg"}]}}]