/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var n;function ba(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ca="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function da(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var ea=da(this);function p(a,b){if(b)a:{var c=ea;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ca(c,a,{configurable:!0,writable:!0,value:b})}}
p("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;ca(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.g};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0;return b});
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ea[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(ba(this))}})}return a});function fa(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
function ha(a){return a.raw=a}function ia(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:ba(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}var ja="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},ka;
if("function"==typeof Object.setPrototypeOf)ka=Object.setPrototypeOf;else{var la;a:{var ma={a:!0},na={};try{na.__proto__=ma;la=na.a;break a}catch(a){}la=!1}ka=la?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var oa=ka;
function q(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(oa)oa(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.L=b.prototype}function pa(){this.v=!1;this.i=null;this.m=void 0;this.g=1;this.s=this.h=0;this.j=null}function qa(a){if(a.v)throw new TypeError("Generator is already running");a.v=!0}pa.prototype.l=function(a){this.m=a};
function ra(a,b){a.j={fa:b,sa:!0};a.g=a.h||a.s}pa.prototype.return=function(a){this.j={return:a};this.g=this.s};function r(a,b,c){a.g=c;return{value:b}}function sa(a,b){a.g=b;a.h=0}function ta(a){a.h=0;var b=a.j.fa;a.j=null;return b}function ua(a){this.g=new pa;this.h=a}function va(a,b){qa(a.g);var c=a.g.i;if(c)return wa(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return xa(a)}
function wa(a,b,c,d){try{var e=b.call(a.g.i,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.v=!1,e;var f=e.value}catch(g){return a.g.i=null,ra(a.g,g),xa(a)}a.g.i=null;d.call(a.g,f);return xa(a)}function xa(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.v=!1,{value:b.value,done:!1}}catch(c){a.g.m=void 0,ra(a.g,c)}a.g.v=!1;if(a.g.j){b=a.g.j;a.g.j=null;if(b.sa)throw b.fa;return{value:b.return,done:!0}}return{value:void 0,done:!0}}
function ya(a){this.next=function(b){qa(a.g);a.g.i?b=wa(a,a.g.i.next,b,a.g.l):(a.g.l(b),b=xa(a));return b};this.throw=function(b){qa(a.g);a.g.i?b=wa(a,a.g.i["throw"],b,a.g.l):(ra(a.g,b),b=xa(a));return b};this.return=function(b){return va(a,b)};this[Symbol.iterator]=function(){return this}}function za(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})}
function Aa(a){return za(new ya(new ua(a)))}function Ba(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}
p("Promise",function(a){function b(g){this.g=0;this.i=void 0;this.h=[];this.l=!1;var h=this.j();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.h=function(g){if(null==this.g){this.g=[];var h=this;this.i(function(){h.m()})}this.g.push(g)};var e=ea.setTimeout;c.prototype.i=function(g){e(g,0)};c.prototype.m=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=
g[h];g[h]=null;try{k()}catch(l){this.j(l)}}}this.g=null};c.prototype.j=function(g){this.i(function(){throw g;})};b.prototype.j=function(){function g(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:g(this.F),reject:g(this.m)}};b.prototype.F=function(g){if(g===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.I(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?this.D(g):this.v(g)}};
b.prototype.D=function(g){var h=void 0;try{h=g.then}catch(k){this.m(k);return}"function"==typeof h?this.K(h,g):this.v(g)};b.prototype.m=function(g){this.s(2,g)};b.prototype.v=function(g){this.s(1,g)};b.prototype.s=function(g,h){if(0!=this.g)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.i=h;2===this.g&&this.G();this.B()};b.prototype.G=function(){var g=this;e(function(){if(g.C()){var h=ea.console;"undefined"!==typeof h&&h.error(g.i)}},1)};b.prototype.C=
function(){if(this.l)return!1;var g=ea.CustomEvent,h=ea.Event,k=ea.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=ea.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.i;return k(g)};b.prototype.B=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new c;
b.prototype.I=function(g){var h=this.j();g.V(h.resolve,h.reject)};b.prototype.K=function(g,h){var k=this.j();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(g,h){function k(w,y){return"function"==typeof w?function(I){try{l(w(I))}catch(aa){m(aa)}}:y}var l,m,x=new b(function(w,y){l=w;m=y});this.V(k(g,l),k(h,m));return x};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.V=function(g,h){function k(){switch(l.g){case 1:g(l.i);break;case 2:h(l.i);break;
default:throw Error("Unexpected state: "+l.g);}}var l=this;null==this.h?f.h(k):this.h.push(k);this.l=!0};b.resolve=d;b.reject=function(g){return new b(function(h,k){k(g)})};b.race=function(g){return new b(function(h,k){for(var l=ia(g),m=l.next();!m.done;m=l.next())d(m.value).V(h,k)})};b.all=function(g){var h=ia(g),k=h.next();return k.done?d([]):new b(function(l,m){function x(I){return function(aa){w[I]=aa;y--;0==y&&l(w)}}var w=[],y=0;do w.push(void 0),y++,d(k.value).V(x(w.length-1),m),k=h.next();
while(!k.done)})};return b});function t(a,b){return Object.prototype.hasOwnProperty.call(a,b)}var Ca="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)t(d,e)&&(a[e]=d[e])}return a};p("Object.assign",function(a){return a||Ca});
p("WeakMap",function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=ia(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}}function c(){}function d(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function e(k){if(!t(k,g)){var l=new c;ca(k,g,{value:l})}}function f(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof c)return m;Object.isExtensible(m)&&e(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),
m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(x){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(k,l){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!t(k,g))throw Error("WeakMap key fail: "+k);k[g][this.g]=l;return this};b.prototype.get=function(k){return d(k)&&t(k,g)?k[g][this.g]:void 0};b.prototype.has=function(k){return d(k)&&t(k,
g)&&t(k[g],this.g)};b.prototype.delete=function(k){return d(k)&&t(k,g)&&t(k[g],this.g)?delete k[g][this.g]:!1};return b});
p("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,k){var l=h[1];return fa(function(){if(l){for(;l.head!=h[1];)l=l.previous;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})}function d(h,k){var l=k&&typeof k;"object"==l||"function"==l?f.has(k)?l=f.get(k):(l=""+ ++g,f.set(k,l)):l="p_"+k;var m=h[0][l];if(m&&t(h[0],l))for(h=0;h<m.length;h++){var x=m[h];if(k!==k&&x.key!==x.key||k===x.key)return{id:l,list:m,index:h,entry:x}}return{id:l,
list:m,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=ia(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(ia([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=
m.value[0].x||"t"!=m.value[1]||!l.next().done?!1:!0}catch(x){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:k},l.list.push(l.entry),this[1].previous.next=l.entry,this[1].previous=l.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||
delete this[0][h.id],h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=
function(){return c(this,function(h){return h.value})};e.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=m.value,h.call(k,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)t(b,d)&&c.push(b[d]);return c}});p("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});
p("Number.isNaN",function(a){return a?a:function(b){return"number"===typeof b&&isNaN(b)}});function Da(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}p("Array.prototype.entries",function(a){return a?a:function(){return Da(this,function(b,c){return[b,c]})}});
p("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});p("Array.prototype.keys",function(a){return a?a:function(){return Da(this,function(b){return b})}});
p("Array.prototype.values",function(a){return a?a:function(){return Da(this,function(b,c){return c})}});p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)t(b,d)&&c.push([d,b[d]]);return c}});var u=this||self;function Ea(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"}function Fa(a){var b=Ea(a);return"array"==b||"object"==b&&"number"==typeof a.length}function Ga(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}
function Ha(a,b,c){return a.call.apply(a.bind,arguments)}function Ia(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function Ja(a,b,c){Ja=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?Ha:Ia;return Ja.apply(null,arguments)}
function Ka(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function La(a,b){function c(){}c.prototype=b.prototype;a.L=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.xa=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}}function Ma(a){return a};function Na(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,Na);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}La(Na,Error);Na.prototype.name="CustomError";var Oa;function Pa(a){u.setTimeout(function(){throw a;},0)};var Qa,Ra;a:{for(var Sa=["CLOSURE_FLAGS"],Ta=u,Ua=0;Ua<Sa.length;Ua++)if(Ta=Ta[Sa[Ua]],null==Ta){Ra=null;break a}Ra=Ta}var Va=Ra&&Ra[610401301];Qa=null!=Va?Va:!1;function Wa(){var a=u.navigator;return a&&(a=a.userAgent)?a:""}var Xa,Ya=u.navigator;Xa=Ya?Ya.userAgentData||null:null;function Za(a){return Qa?Xa?Xa.brands.some(function(b){return(b=b.brand)&&-1!=b.indexOf(a)}):!1:!1}function v(a){return-1!=Wa().indexOf(a)};function $a(){return Qa?!!Xa&&0<Xa.brands.length:!1}function ab(){return $a()?!1:v("Opera")}function bb(){return $a()?!1:v("Trident")||v("MSIE")}function cb(){return v("Firefox")||v("FxiOS")}function db(){return $a()?Za("Chromium"):(v("Chrome")||v("CriOS"))&&!($a()?0:v("Edge"))||v("Silk")};function eb(){return v("iPhone")&&!v("iPod")&&!v("iPad")};var fb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},gb=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
function hb(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};function ib(a){ib[" "](a);return a}ib[" "]=function(){};function jb(a,b,c){return Object.prototype.hasOwnProperty.call(a,b)?a[b]:a[b]=c(b)};var kb=ab(),lb=bb(),mb=v("Edge"),nb=v("Gecko")&&!(-1!=Wa().toLowerCase().indexOf("webkit")&&!v("Edge"))&&!(v("Trident")||v("MSIE"))&&!v("Edge"),ob=-1!=Wa().toLowerCase().indexOf("webkit")&&!v("Edge"),pb;
a:{var qb="",rb=function(){var a=Wa();if(nb)return/rv:([^\);]+)(\)|;)/.exec(a);if(mb)return/Edge\/([\d\.]+)/.exec(a);if(lb)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(ob)return/WebKit\/(\S+)/.exec(a);if(kb)return/(?:Version)[ \/]?(\S+)/.exec(a)}();rb&&(qb=rb?rb[1]:"");if(lb){var sb,tb=u.document;sb=tb?tb.documentMode:void 0;if(null!=sb&&sb>parseFloat(qb)){pb=String(sb);break a}}pb=qb}var ub=pb;var vb=cb(),wb=eb()||v("iPod"),xb=v("iPad"),yb=v("Android")&&!(db()||cb()||ab()||v("Silk")),zb=db(),Ab=v("Safari")&&!(db()||($a()?0:v("Coast"))||ab()||($a()?0:v("Edge"))||($a()?Za("Microsoft Edge"):v("Edg/"))||($a()?Za("Opera"):v("OPR"))||cb()||v("Silk")||v("Android"))&&!(eb()||v("iPad")||v("iPod"));var Bb={},Cb=null;var Db="undefined"!==typeof Uint8Array,Eb=!lb&&"function"===typeof btoa;function z(a){return Array.prototype.slice.call(a)};var A="function"===typeof Symbol&&"symbol"===typeof Symbol()?Symbol():void 0,Fb=A?function(a,b){a[A]|=b}:function(a,b){void 0!==a.H?a.H|=b:Object.defineProperties(a,{H:{value:b,configurable:!0,writable:!0,enumerable:!1}})};function Gb(a){var b=B(a);1!==(b&1)&&(Object.isFrozen(a)&&(a=z(a)),C(a,b|1))}
var Hb=A?function(a,b){a[A]&=~b}:function(a,b){void 0!==a.H&&(a.H&=~b)},B=A?function(a){return a[A]|0}:function(a){return a.H|0},D=A?function(a){return a[A]}:function(a){return a.H},C=A?function(a,b){a[A]=b}:function(a,b){void 0!==a.H?a.H=b:Object.defineProperties(a,{H:{value:b,configurable:!0,writable:!0,enumerable:!1}})};function Ib(a,b){Object.isFrozen(a)&&(a=z(a));C(a,b);return a}function Jb(a){Fb(a,1);return a}function Kb(a,b){C(b,(a|0)&-255)}function Lb(a,b){C(b,(a|34)&-221)}
function Mb(a){a=a>>10&1023;return 0===a?536870912:a};var Nb={};function Ob(a){return null!==a&&"object"===typeof a&&!Array.isArray(a)&&a.constructor===Object}var Pb,Qb=[];C(Qb,39);Pb=Object.freeze(Qb);function Rb(a){if(a&2)throw Error();};function Sb(a){return"number"===typeof a&&!Number.isNaN(a)||!!a&&"string"===typeof a&&!isNaN(a)}function Tb(a){return null==a||"string"===typeof a?a:void 0}function Ub(a,b,c){var d=!1;if(null!=a&&"object"===typeof a&&!(d=Array.isArray(a))&&a.W===Nb)return a;if(d){var e=d=B(a);0===e&&(e|=c&32);e|=c&2;e!==d&&C(a,e);return new b(a)}};var Vb;function E(a,b,c){null==a&&(a=Vb);Vb=void 0;if(null==a){var d=96;c?(a=[c],d|=512):a=[];b&&(d=d&-1047553|(b&1023)<<10)}else{if(!Array.isArray(a))throw Error();d=B(a);if(d&64)return a;d|=64;if(c&&(d|=512,c!==a[0]))throw Error();a:{c=a;var e=c.length;if(e){var f=e-1,g=c[f];if(Ob(g)){d|=256;b=(d>>9&1)-1;e=f-b;1024<=e&&(Wb(c,b,g),e=1023);d=d&-1047553|(e&1023)<<10;break a}}b&&(g=(d>>9&1)-1,b=Math.max(b,e-g),1024<b&&(Wb(c,g,{}),d|=256,b=1023),d=d&-1047553|(b&1023)<<10)}}C(a,d);return a}
function Wb(a,b,c){for(var d=1023+b,e=a.length,f=d;f<e;f++){var g=a[f];null!=g&&g!==c&&(c[f-b]=g)}a.length=d+1;a[d]=c};function Xb(a){switch(typeof a){case "number":return isFinite(a)?a:String(a);case "boolean":return a?1:0;case "object":if(a&&!Array.isArray(a)&&Db&&null!=a&&a instanceof Uint8Array){if(Eb){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else{void 0===b&&(b=0);if(!Cb){Cb={};c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");d=["+/=","+/","-_=","-_.","-_"];for(var e=
0;5>e;e++){var f=c.concat(d[e].split(""));Bb[e]=f;for(var g=0;g<f.length;g++){var h=f[g];void 0===Cb[h]&&(Cb[h]=g)}}}b=Bb[b];c=Array(Math.floor(a.length/3));d=b[64]||"";for(e=f=0;f<a.length-2;f+=3){var k=a[f],l=a[f+1];h=a[f+2];g=b[k>>2];k=b[(k&3)<<4|l>>4];l=b[(l&15)<<2|h>>6];h=b[h&63];c[e++]=g+k+l+h}g=0;h=d;switch(a.length-f){case 2:g=a[f+1],h=b[(g&15)<<2]||d;case 1:a=a[f],c[e]=b[a>>2]+b[(a&3)<<4|g>>4]+h+d}a=c.join("")}return a}}return a};function Yb(a,b,c){a=z(a);var d=a.length,e=b&256?a[d-1]:void 0;d+=e?-1:0;for(b=b&512?1:0;b<d;b++)a[b]=c(a[b]);if(e){b=a[b]={};for(var f in e)b[f]=c(e[f])}return a}function Zb(a,b,c,d,e,f){if(null!=a){if(Array.isArray(a))a=e&&0==a.length&&B(a)&1?void 0:f&&B(a)&2?a:$b(a,b,c,void 0!==d,e,f);else if(Ob(a)){var g={},h;for(h in a)g[h]=Zb(a[h],b,c,d,e,f);a=g}else a=b(a,d);return a}}
function $b(a,b,c,d,e,f){var g=d||c?B(a):0;d=d?!!(g&32):void 0;a=z(a);for(var h=0;h<a.length;h++)a[h]=Zb(a[h],b,c,d,e,f);c&&c(g,a);return a}function ac(a){return a.W===Nb?bc(a):Db&&null!=a&&a instanceof Uint8Array?new Uint8Array(a):a}function cc(a){return a.W===Nb?a.toJSON():Xb(a)};function dc(a,b,c){c=void 0===c?Lb:c;if(null!=a){if(Db&&a instanceof Uint8Array)return b?a:new Uint8Array(a);if(Array.isArray(a)){var d=B(a);return d&2?a:!b||d&68||!(d&32||0===d)?$b(a,dc,d&4?Lb:c,!0,!1,!0):(C(a,d|34),a)}a.W===Nb&&(b=a.o,c=D(b),a=c&2?a:ec(a,b,c,!0));return a}}function ec(a,b,c,d){a=a.constructor;Vb=b=fc(b,c,d);b=new a(b);Vb=void 0;return b}function fc(a,b,c){var d=c||b&2?Lb:Kb,e=!!(b&32);a=Yb(a,b,function(f){return dc(f,e,d)});Fb(a,32|(c?2:0));return a}
function gc(a){var b=a.o,c=D(b);return c&2?ec(a,b,c,!1):a};function F(a,b){a=a.o;return hc(a,D(a),b)}function hc(a,b,c,d){if(-1===c)return null;if(c>=Mb(b)){if(b&256)return a[a.length-1][c]}else{var e=a.length;if(d&&b&256&&(d=a[e-1][c],null!=d))return d;b=c+((b>>9&1)-1);if(b<e)return a[b]}}function G(a,b,c){var d=a.o,e=D(d);Rb(e);H(d,e,b,c);return a}
function H(a,b,c,d,e){var f=Mb(b);if(c>=f||e){e=b;if(b&256)f=a[a.length-1];else{if(null==d)return;f=a[f+((b>>9&1)-1)]={};e|=256}f[c]=d;e!==b&&C(a,e)}else a[c+((b>>9&1)-1)]=d,b&256&&(a=a[a.length-1],c in a&&delete a[c])}function ic(a,b,c){var d=b&2;a=hc(a,b,c);Array.isArray(a)||(a=Pb);b=B(a);b&1||Jb(a);d?b&2||Fb(a,34):b&32&&!(b&2)&&Hb(a,32);return a}function jc(a,b){a=F(a,b);return null==a?a:"boolean"===typeof a||"number"===typeof a?!!a:void 0}function kc(a){a=a.o;return lc(a,D(a),mc)}
function lc(a,b,c){for(var d=0,e=0;e<c.length;e++){var f=c[e];null!=hc(a,b,f)&&(0!==d&&H(a,b,d),d=f)}return d}function J(a,b,c,d){d=void 0===d?!1:d;var e=d;var f=a.o,g=D(f),h=hc(f,g,c,e);b=Ub(h,b,g);b!==h&&null!=b&&H(f,g,c,b,e);e=b;if(null==e)return e;a=a.o;f=D(a);f&2||(g=gc(e),g!==e&&(e=g,H(a,f,c,e,d)));return e}
function nc(a,b,c){var d=a.o,e=D(d);a=!!(e&2);var f=a?1:2,g=!!(e&2),h=ic(d,e,c);if(h!==Pb&&B(h)&4)3!==f&&(g?2===f&&(f=B(h),h=z(h),C(h,f),H(d,e,c,h)):(g=Object.isFrozen(h),1===f?g||Object.freeze(h):(f=B(h),b=f&-35,g&&(h=z(h),f=0,H(d,e,c,h)),f!==b&&C(h,b)))),c=h;else{var k=h;h=!!(e&2);var l=!!(B(k)&2);g=k;!h&&l&&(k=z(k));var m=e|(l?2:0);l=l||void 0;for(var x=0,w=0;x<k.length;x++){var y=Ub(k[x],b,m);void 0!==y&&(l=l||D(y.o)&2,k[w++]=y)}w<x&&(k.length=w);b=k;k=B(b);m=k|5;l=l?m&-9:m|8;k!=l&&(b=Ib(b,l));
k=b;g!==k&&H(d,e,c,k);(h&&2!==f||1===f)&&Object.freeze(k);c=k}if(!(a||B(c)&8)){for(a=0;a<c.length;a++)d=c[a],e=gc(d),d!==e&&(c[a]=e);Fb(c,8)}return c}function oc(a,b,c){null==c&&(c=void 0);return G(a,b,c)}function pc(a,b,c){var d=mc;null==c&&(c=void 0);var e=a.o,f=D(e);Rb(f);(d=lc(e,f,d))&&d!==b&&null!=c&&H(e,f,d);H(e,f,b,c);return a}
function qc(a,b,c){var d=a.o,e=D(d);Rb(e);if(null!=c){for(var f=!!c.length,g=0;g<c.length;g++){var h=c[g];f=f&&!(B(h.o)&2)}g=B(c);h=g|1;h=(f?h|8:h&-9)|4;h!=g&&(c=Ib(c,h))}null==c&&(c=void 0);H(d,e,b,c);return a}function rc(a){a=F(a,1);var b;null==a?b=a:Sb(a)?"number"===typeof a?b=a:b=a:b=void 0;return b}function sc(a,b,c){if(null!=c){if("boolean"!==typeof c)throw Error("Expected boolean but got "+Ea(c)+": "+c);c=!!c}return G(a,b,c)}function K(a,b){return Tb(F(a,b))}
function tc(a,b,c){if(null!=c&&"string"!==typeof c)throw Error();return G(a,b,c)}function uc(a,b){return null!=a?a:b}function vc(a,b,c){var d=a.o;c=lc(d,D(d),mc)===c?c:-1;return J(a,b,c)};function L(a,b,c){this.o=E(a,b,c)}L.prototype.toJSON=function(){var a=$b(this.o,cc,void 0,void 0,!1,!1);return wc(this,a,!0)};function bc(a){return wc(a,$b(a.o,ac,void 0,void 0,!1,!1),!0)}L.prototype.W=Nb;L.prototype.toString=function(){return wc(this,this.o,!1).toString()};
function wc(a,b,c){var d=a.constructor.N,e=Mb(D(c?a.o:b)),f=!1;if(d){if(!c){b=z(b);var g;if(b.length&&Ob(g=b[b.length-1]))for(f=0;f<d.length;f++)if(d[f]>=e){Object.assign(b[b.length-1]={},g);break}f=!0}e=b;c=!c;g=D(a.o);a=Mb(g);g=(g>>9&1)-1;for(var h,k,l=0;l<d.length;l++)if(k=d[l],k<a){k+=g;var m=e[k];null==m?e[k]=c?Pb:Jb([]):c&&m!==Pb&&Gb(m)}else h||(m=void 0,e.length&&Ob(m=e[e.length-1])?h=m:e.push(h={})),m=h[k],null==h[k]?h[k]=c?Pb:Jb([]):c&&m!==Pb&&Gb(m)}d=b.length;if(!d)return b;var x;if(Ob(h=
b[d-1])){a:{var w=h;e={};c=!1;for(var y in w)a=w[y],Array.isArray(a)&&a!=a&&(c=!0),null!=a?e[y]=a:c=!0;if(c){for(var I in e){w=e;break a}w=null}}w!=h&&(x=!0);d--}for(;0<d;d--){h=b[d-1];if(null!=h)break;var aa=!0}if(!x&&!aa)return b;var T;f?T=b:T=Array.prototype.slice.call(b,0,d);b=T;f&&(b.length=d);w&&b.push(w);return b};function xc(a,b){this.h=a;this.g=b;this.i=J;this.defaultValue=void 0};function yc(a){this.o=E(a)}q(yc,L);function zc(a){this.o=E(a)}q(zc,L);zc.prototype.setEnabled=function(a){return sc(this,2,a)};function Ac(a){this.o=E(a)}q(Ac,L);Ac.prototype.setEnabled=function(a){return sc(this,2,a)};function Bc(a){this.o=E(a)}q(Bc,L);Bc.N=[3];function Cc(a){this.o=E(a)}q(Cc,L);Cc.N=[3];function Dc(a){this.o=E(a)}q(Dc,L);function Ec(a,b){return qc(a,2,b)}Dc.N=[2];function Fc(a){this.o=E(a)}q(Fc,L);function Gc(a,b){return sc(a,2,b)}function Hc(a,b){return oc(a,3,b)};function Ic(a){this.o=E(a)}q(Ic,L);function Jc(a){var b=new Ic;return G(b,3,a)}function Kc(a,b){return pc(a,5,b)}var mc=[4,5,6,7,8];function M(){}M.prototype.l=function(){return this.i||(Object.defineProperties(this,{i:{value:Lc=Lc+1|0,enumerable:!1}}),this.i)};M.prototype.toString=function(){var a=N(Mc(O(Nc(this))))+"@";var b=(this.l()>>>0).toString(16);return a+N(b)};M.prototype.u=["java.lang.Object",0];function Oc(){}q(Oc,M);Oc.prototype.h=function(a){this.g=a;Pc(a,this)};function Qc(a){Rc(a.g)&&(Error.captureStackTrace?Error.captureStackTrace(Sc(a.g,Rc,Tc)):Sc(a.g,Rc,Tc).stack=Error().stack)}Oc.prototype.toString=function(){var a=Mc(O(Nc(this))),b=this.m;return null==b?a:N(a)+": "+N(b)};Oc.prototype.u=["java.lang.Throwable",0];function Uc(){}q(Uc,Oc);Uc.prototype.u=["java.lang.Exception",0];function Vc(){}q(Vc,Uc);Vc.prototype.u=["java.lang.RuntimeException",0];function Wc(){}q(Wc,Vc);Wc.prototype.u=["java.lang.IndexOutOfBoundsException",0];function Sc(a,b,c){if(null!=a&&!b(a))throw a=N(Mc(Xc(a)))+" cannot be cast to "+N(Mc(O(c))),b=new Yc,b.m=a,Qc(b),b.h(Error(b)),b.g;return a};function Nc(a){return a.constructor};function Zc(){}q(Zc,M);Zc.prototype.u=["java.lang.Boolean",0];function $c(){}q($c,M);$c.prototype.u=["java.lang.Number",0];function ad(){}q(ad,$c);ad.prototype.u=["java.lang.Double",0];function bd(a){this.g=a}q(bd,M);bd.prototype.u=["java.lang.Iterable$$LambdaAdaptor",0];function cd(){}q(cd,Vc);cd.prototype.h=function(a){Vc.prototype.h.call(this,Object.is(this.j,"__noinit__")?a:this.j)};cd.prototype.u=["java.lang.JsException",0];function dd(){}q(dd,cd);dd.prototype.u=["java.lang.NullPointerException",0];function ed(){}q(ed,Vc);ed.prototype.u=["java.util.NoSuchElementException",0];function Yc(){}q(Yc,Vc);Yc.prototype.u=["java.lang.ClassCastException",0];var Lc=0;function Xc(a){var b=typeof a;if(null==b)throw a=new dd,Qc(a),a.j="__noinit__",a.h(new TypeError(a)),a.g;switch(b){case "number":return O(ad);case "boolean":return O(Zc);case "string":return O(fd);case "function":return O(gd)}if(a instanceof M)a=O(Nc(a));else if(Array.isArray(a))a=(a=a.va)?O(a.za,a.ya):O(M,1);else if(null!=a)a=O(hd);else throw new TypeError("null.getClass()");return a};function gd(){}gd.prototype.u=["<native function>",1];function hd(){}q(hd,M);hd.prototype.u=["<native object>",0];function id(){}q(id,Wc);id.prototype.u=["java.lang.StringIndexOutOfBoundsException",0];function Tc(){}function Rc(a){return a instanceof Error}Tc.prototype.u=["Error",0];function Pc(a,b){if(a instanceof Object)try{a.wa=b,Object.defineProperties(a,{cause:{get:function(){return b.v&&b.v.g}}})}catch(c){}};function fd(){}q(fd,M);function N(a){return null==a?"null":a.toString()}fd.prototype.u=["java.lang.String",0];function jd(a,b){this.g=a;this.h=b}q(jd,M);function Mc(a){return 0!=a.h?N(kd("[",a.h))+N(3==a.g.prototype.u[1]?a.g.prototype.u[2]:"L"+N(a.g.prototype.u[0])+";"):a.g.prototype.u[0]}function ld(a,b){b=a.lastIndexOf(b)+1|0;var c=a.length+1|0;if(0>b||b>=c)throw a=new id,a.m="Index: "+b+", Size: "+c,Qc(a),a.h(Error(a)),a.g;return a.substr(b)}jd.prototype.toString=function(){return String(0==this.h&&1==this.g.prototype.u[1]?"interface ":0==this.h&&3==this.g.prototype.u[1]?"":"class ")+N(Mc(this))};
function kd(a,b){for(var c="",d=0;d<b;d=d+1|0)c=N(c)+N(a);return c}jd.prototype.u=["java.lang.Class",0];function O(a,b){var c=b||0;return jb(a.prototype,"$$class/"+c,function(){return new jd(a,c)})};function md(a){a&&"function"==typeof a.P&&a.P()};function nd(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];Fa(d)?nd.apply(null,d):md(d)}};function P(){this.v=this.v;this.m=this.m}P.prototype.v=!1;P.prototype.ha=function(){return this.v};P.prototype.P=function(){this.v||(this.v=!0,this.A())};function Q(a,b){od(a,Ka(md,b))}function od(a,b,c){a.v?void 0!==c?b.call(c):b():(a.m||(a.m=[]),a.m.push(void 0!==c?Ja(b,c):b))}P.prototype.A=function(){if(this.m)for(;this.m.length;)this.m.shift()()};function pd(){P.call(this);this.g=null}q(pd,P);function qd(a,b,c){a.ha()||(rd(a),a.g=new Image,a.g.onload=function(){c&&c(!0);rd(a)},a.g.onerror=function(){c&&c(!1);rd(a)},a.g.src=b)}function rd(a){if(a.g)try{a.g.onload=null,a.g.onerror=null,a.g=null}catch(b){}}pd.prototype.A=function(){rd(this)};function sd(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1}sd.prototype.i=function(){this.defaultPrevented=!0};function td(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,td));a.stack||(a.stack=ud(td));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function vd(a,b){a=td(a);if(b)for(var c in b){var d=a,e=c,f=b[c];d.__closure__error__context__984382||(d.__closure__error__context__984382={});d.__closure__error__context__984382[e]=f}return a}
function ud(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||ud),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=wd(a||arguments.callee.caller,[]));return b}
function wd(a,b){var c=[];if(0<=fb(b,a))c.push("[...circular reference...]");else if(a&&50>b.length){c.push(xd(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){0<e&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=xd(f))?f:"[fn]";break;default:f=typeof f}40<f.length&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(wd(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")}function xd(a){if(yd[a])return yd[a];a=String(a);if(!yd[a]){var b=/function\s+([^\(]+)/m.exec(a);yd[a]=b?b[1]:"[Anonymous]"}return yd[a]}var yd={};var zd=function(){if(!u.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};u.addEventListener("test",c,b);u.removeEventListener("test",c,b)}catch(d){}return a}();function Ad(a,b){sd.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.h=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;if(b=a.relatedTarget){if(nb){a:{try{ib(b.nodeName);var e=!0;break a}catch(f){}e=
!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=
a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:Bd[a.pointerType]||"";this.state=a.state;this.h=a;a.defaultPrevented&&Ad.L.i.call(this)}}La(Ad,sd);var Bd={2:"touch",3:"pen",4:"mouse"};Ad.prototype.i=function(){Ad.L.i.call(this);var a=this.h;a.preventDefault?a.preventDefault():a.returnValue=!1};var Cd="closure_listenable_"+(1E6*Math.random()|0);function Dd(a){return!(!a||!a[Cd])};var Ed=0;function Fd(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.M=e;this.key=++Ed;this.removed=this.U=!1}function Gd(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.M=null};function Hd(a,b,c){for(var d in a)b.call(c,a[d],d,a)}var Id="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Jd(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Id.length;f++)c=Id[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function Kd(a){this.src=a;this.g={};this.h=0}Kd.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=Ld(a,b,d,e);-1<g?(b=a[g],c||(b.U=!1)):(b=new Fd(b,this.src,f,!!d,e),b.U=c,a.push(b));return b};Kd.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=Ld(e,b,c,d);return-1<b?(Gd(e[b]),Array.prototype.splice.call(e,b,1),0==e.length&&(delete this.g[a],this.h--),!0):!1};
function Md(a,b){var c=b.type;if(c in a.g){var d=a.g[c],e=fb(d,b),f;(f=0<=e)&&Array.prototype.splice.call(d,e,1);f&&(Gd(b),0==a.g[c].length&&(delete a.g[c],a.h--))}}Kd.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.g)if(!a||c==a){for(var d=this.g[c],e=0;e<d.length;e++)++b,Gd(d[e]);delete this.g[c];this.h--}return b};function Nd(a,b,c,d,e){a=a.g[b.toString()];b=-1;a&&(b=Ld(a,c,d,e));return-1<b?a[b]:null}
function Ld(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.M==d)return e}return-1};var Od="closure_lm_"+(1E6*Math.random()|0),Pd={},Qd=0;function Rd(a,b,c,d,e){if(d&&d.once)return Sd(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)Rd(a,b[f],c,d,e);return null}c=Td(c);return Dd(a)?a.listen(b,c,Ga(d)?!!d.capture:!!d,e):Ud(a,b,c,!1,d,e)}
function Ud(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=Ga(e)?!!e.capture:!!e,h=Vd(a);h||(a[Od]=h=new Kd(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Wd();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)zd||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Xd(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Qd++;return c}
function Wd(){function a(c){return b.call(a.src,a.listener,c)}var b=Yd;return a}function Sd(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)Sd(a,b[f],c,d,e);return null}c=Td(c);return Dd(a)?a.h.add(String(b),c,!0,Ga(d)?!!d.capture:!!d,e):Ud(a,b,c,!0,d,e)}function Zd(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Zd(a,b[f],c,d,e);else d=Ga(d)?!!d.capture:!!d,c=Td(c),Dd(a)?a.h.remove(String(b),c,d,e):a&&(a=Vd(a))&&(b=Nd(a,b,c,d,e))&&$d(b)}
function $d(a){if("number"!==typeof a&&a&&!a.removed){var b=a.src;if(Dd(b))Md(b.h,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Xd(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Qd--;(c=Vd(b))?(Md(c,a),0==c.h&&(c.src=null,b[Od]=null)):Gd(a)}}}function Xd(a){return a in Pd?Pd[a]:Pd[a]="on"+a}function Yd(a,b){if(a.removed)a=!0;else{b=new Ad(b,this);var c=a.listener,d=a.M||a.src;a.U&&$d(a);a=c.call(d,b)}return a}
function Vd(a){a=a[Od];return a instanceof Kd?a:null}var ae="__closure_events_fn_"+(1E9*Math.random()>>>0);function Td(a){if("function"===typeof a)return a;a[ae]||(a[ae]=function(b){return a.handleEvent(b)});return a[ae]};function R(){P.call(this);this.h=new Kd(this);this.K=this;this.s=null}La(R,P);R.prototype[Cd]=!0;R.prototype.removeEventListener=function(a,b,c,d){Zd(this,a,b,c,d)};
function be(a,b){var c,d=a.s;if(d)for(c=[];d;d=d.s)c.push(d);a=a.K;d=b.type||b;if("string"===typeof b)b=new sd(b,a);else if(b instanceof sd)b.target=b.target||a;else{var e=b;b=new sd(d,a);Jd(b,e)}e=!0;if(c)for(var f=c.length-1;0<=f;f--){var g=b.g=c[f];e=ce(g,d,!0,b)&&e}g=b.g=a;e=ce(g,d,!0,b)&&e;e=ce(g,d,!1,b)&&e;if(c)for(f=0;f<c.length;f++)g=b.g=c[f],e=ce(g,d,!1,b)&&e}R.prototype.A=function(){R.L.A.call(this);this.h&&this.h.removeAll(void 0);this.s=null};
R.prototype.listen=function(a,b,c,d){return this.h.add(String(a),b,!1,c,d)};function ce(a,b,c,d){b=a.h.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,k=g.M||g.src;g.U&&Md(a.h,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented};function de(a,b){this.i=a;this.j=b;this.h=0;this.g=null}de.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.i();return a};function ee(a,b){a.j(b);100>a.h&&(a.h++,b.next=a.g,a.g=b)};function fe(){};var ge;function he(a){this.ba=a}he.prototype.toString=function(){return this.ba+""};he.prototype.ra=!0;function ie(a){return a instanceof he&&a.constructor===he?a.ba:"type_error:TrustedResourceUrl"}var je={};function ke(a){if(void 0===ge){var b=null;var c=u.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:Ma,createScript:Ma,createScriptURL:Ma})}catch(d){u.console&&u.console.error(d.message)}ge=b}else ge=b}a=(b=ge)?b.createScriptURL(a):a;return new he(a,je)};function le(a,b){Hd(b,function(c,d){c&&"object"==typeof c&&c.ra&&(c=c.ba.toString());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:me.hasOwnProperty(d)?a.setAttribute(me[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})}
var me={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};function ne(a){return a.parentWindow||a.defaultView}
function oe(a,b,c){function d(h){h&&b.appendChild("string"===typeof h?a.createTextNode(h):h)}for(var e=2;e<c.length;e++){var f=c[e];if(!Fa(f)||Ga(f)&&0<f.nodeType)d(f);else{a:{if(f&&"number"==typeof f.length){if(Ga(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}gb(g?hb(f):f,d)}}}function pe(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)}
function qe(){this.g=u.document||document}qe.prototype.setProperties=le;qe.prototype.h=function(a,b,c){var d=this.g,e=arguments,f=e[1],g=pe(d,String(e[0]));f&&("string"===typeof f?g.className=f:Array.isArray(f)?g.className=f.join(" "):le(g,f));2<e.length&&oe(d,g,e);return g};qe.prototype.getChildren=function(a){return void 0!=a.children?a.children:Array.prototype.filter.call(a.childNodes,function(b){return 1==b.nodeType})};
qe.prototype.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var re;
function se(){var a=u.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!v("Presto")&&(a=function(){var e=pe(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=Ja(function(k){if(("*"==h||k.origin==h)&&k.data==g)this.port1.onmessage()},this);
f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!bb()){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.ea;c.ea=null;e()}};return function(e){d.next={ea:e};d=d.next;b.port2.postMessage(0)}}return function(e){u.setTimeout(e,0)}};function te(){this.h=this.g=null}te.prototype.add=function(a,b){var c=ue.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};te.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.h=null),a.next=null);return a};var ue=new de(function(){return new ve},function(a){return a.reset()});function ve(){this.next=this.g=this.h=null}ve.prototype.set=function(a,b){this.h=a;this.g=b;this.next=null};ve.prototype.reset=function(){this.next=this.g=this.h=null};var we,xe=!1,ye=new te;function ze(a,b){we||Ae();xe||(we(),xe=!0);ye.add(a,b)}function Ae(){if(u.Promise&&u.Promise.resolve){var a=u.Promise.resolve(void 0);we=function(){a.then(Be)}}else we=function(){var b=Be;"function"!==typeof u.setImmediate||u.Window&&u.Window.prototype&&($a()||!v("Edge"))&&u.Window.prototype.setImmediate==u.setImmediate?(re||(re=se()),re(b)):u.setImmediate(b)}}function Be(){for(var a;a=ye.remove();){try{a.h.call(a.g)}catch(b){Pa(b)}ee(ue,a)}xe=!1};function S(a){this.g=0;this.l=void 0;this.j=this.h=this.i=null;this.m=this.v=!1;if(a!=fe)try{var b=this;a.call(void 0,function(c){Ce(b,2,c)},function(c){Ce(b,3,c)})}catch(c){Ce(this,3,c)}}function De(){this.next=this.i=this.h=this.m=this.g=null;this.j=!1}De.prototype.reset=function(){this.i=this.h=this.m=this.g=null;this.j=!1};var Ee=new de(function(){return new De},function(a){a.reset()});function Fe(a,b,c){var d=Ee.get();d.m=a;d.h=b;d.i=c;return d}
function Ge(a){if(a instanceof S)return a;var b=new S(fe);Ce(b,2,a);return b}function He(){var a,b,c=new S(function(d,e){a=d;b=e});return new Ie(c,a,b)}S.prototype.then=function(a,b,c){return Je(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};S.prototype.$goog_Thenable=!0;function Ke(a,b){b=Fe(b,b);b.j=!0;Le(a,b)}function U(a,b){return Je(a,null,b)}S.prototype.cancel=function(a){if(0==this.g){var b=new Me(a);ze(function(){Ne(this,b)},this)}};
function Ne(a,b){if(0==a.g)if(a.i){var c=a.i;if(c.h){for(var d=0,e=null,f=null,g=c.h;g&&(g.j||(d++,g.g==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.g&&1==d?Ne(c,b):(f?(d=f,d.next==c.j&&(c.j=d),d.next=d.next.next):Oe(c),Pe(c,e,3,b)))}a.i=null}else Ce(a,3,b)}function Le(a,b){a.h||2!=a.g&&3!=a.g||Qe(a);a.j?a.j.next=b:a.h=b;a.j=b}
function Je(a,b,c,d){var e=Fe(null,null,null);e.g=new S(function(f,g){e.m=b?function(h){try{var k=b.call(d,h);f(k)}catch(l){g(l)}}:f;e.h=c?function(h){try{var k=c.call(d,h);void 0===k&&h instanceof Me?g(h):f(k)}catch(l){g(l)}}:g});e.g.i=a;Le(a,e);return e.g}S.prototype.B=function(a){this.g=0;Ce(this,2,a)};S.prototype.C=function(a){this.g=0;Ce(this,3,a)};
function Ce(a,b,c){if(0==a.g){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.g=1;a:{var d=c,e=a.B,f=a.C;if(d instanceof S){Le(d,Fe(e||fe,f||null,a));var g=!0}else{if(d)try{var h=!!d.$goog_Thenable}catch(l){h=!1}else h=!1;if(h)d.then(e,f,a),g=!0;else{if(Ga(d))try{var k=d.then;if("function"===typeof k){Re(d,k,e,f,a);g=!0;break a}}catch(l){f.call(a,l);g=!0;break a}g=!1}}}g||(a.l=c,a.g=b,a.i=null,Qe(a),3!=b||c instanceof Me||Se(a,c))}}
function Re(a,b,c,d,e){function f(k){h||(h=!0,d.call(e,k))}function g(k){h||(h=!0,c.call(e,k))}var h=!1;try{b.call(a,g,f)}catch(k){f(k)}}function Qe(a){a.v||(a.v=!0,ze(a.s,a))}function Oe(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.j=null);return b}S.prototype.s=function(){for(var a;a=Oe(this);)Pe(this,a,this.g,this.l);this.v=!1};
function Pe(a,b,c,d){if(3==c&&b.h&&!b.j)for(;a&&a.m;a=a.i)a.m=!1;if(b.g)b.g.i=null,Te(b,c,d);else try{b.j?b.m.call(b.i):Te(b,c,d)}catch(e){Ue.call(null,e)}ee(Ee,b)}function Te(a,b,c){2==b?a.m.call(a.i,c):a.h&&a.h.call(a.i,c)}function Se(a,b){a.m=!0;ze(function(){a.m&&Ue.call(null,b)})}var Ue=Pa;function Me(a){Na.call(this,a)}La(Me,Na);Me.prototype.name="cancel";function Ie(a,b,c){this.promise=a;this.resolve=b;this.reject=c};function Ve(a,b){R.call(this);this.i=a||1;this.g=b||u;this.j=Ja(this.ua,this);this.l=Date.now()}La(Ve,R);n=Ve.prototype;n.T=!1;n.J=null;n.ua=function(){if(this.T){var a=Date.now()-this.l;0<a&&a<.8*this.i?this.J=this.g.setTimeout(this.j,this.i-a):(this.J&&(this.g.clearTimeout(this.J),this.J=null),be(this,"tick"),this.T&&(this.stop(),this.start()))}};n.start=function(){this.T=!0;this.J||(this.J=this.g.setTimeout(this.j,this.i),this.l=Date.now())};
n.stop=function(){this.T=!1;this.J&&(this.g.clearTimeout(this.J),this.J=null)};n.A=function(){Ve.L.A.call(this);this.stop();delete this.g};function We(a,b){if("function"!==typeof a)if(a&&"function"==typeof a.handleEvent)a=Ja(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:u.setTimeout(a,b||0)}
function Xe(){var a=null;return U(new S(function(b,c){a=We(function(){b(void 0)},3E4);-1==a&&c(Error("Failed to schedule timer."))}),function(b){u.clearTimeout(a);throw b;})};function Ye(a,b,c){P.call(this);this.g=a;this.i=b||0;this.h=c;this.j=Ja(this.ia,this)}La(Ye,P);n=Ye.prototype;n.O=0;n.A=function(){Ye.L.A.call(this);this.stop();delete this.g;delete this.h};n.start=function(a){this.stop();this.O=We(this.j,void 0!==a?a:this.i)};n.stop=function(){this.isActive()&&u.clearTimeout(this.O);this.O=0};n.isActive=function(){return 0!=this.O};n.ia=function(){this.O=0;this.g&&this.g.call(this.h)};function Ze(){P.call(this);this.g=null}q(Ze,P);Ze.prototype.A=function(){md(this.g)};function V(a){P.call(this);this.h=a;this.g={}}La(V,P);var $e=[];V.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&($e[0]=b.toString()),b=$e);for(var e=0;e<b.length;e++){var f=Rd(a,b[e],c||this.handleEvent,d||!1,this.h||this);if(!f)break;this.g[f.key]=f}return this};
function af(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)af(a,b,c[g],d,e,f);else d=d||a.handleEvent,e=Ga(e)?!!e.capture:!!e,f=f||a.h||a,d=Td(d),e=!!e,c=Dd(b)?Nd(b.h,String(c),d,e,f):b?(b=Vd(b))?Nd(b,c,d,e,f):null:null,c&&($d(c),delete a.g[c.key])}V.prototype.removeAll=function(){Hd(this.g,function(a,b){this.g.hasOwnProperty(b)&&$d(a)},this);this.g={}};V.prototype.A=function(){V.L.A.call(this);this.removeAll()};
V.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function bf(){R.call(this);this.g=new V(this);this.g.listen(u,["online","offline"],this.i)}La(bf,R);bf.prototype.i=function(){be(this,navigator.onLine?"online":"offline")};bf.prototype.A=function(){bf.L.A.call(this);this.g.P();this.g=null};function cf(a,b){this.name=a;this.value=b}cf.prototype.toString=function(){return this.name};var df=new cf("OFF",Infinity),ef=new cf("SEVERE",1E3),ff=new cf("WARNING",900),gf=new cf("INFO",800),hf=new cf("CONFIG",700),jf=new cf("FINE",500);function kf(){this.clear()}var lf;kf.prototype.clear=function(){};function mf(a,b,c){this.g=void 0;this.reset(a||df,b,c,void 0,void 0)}mf.prototype.reset=function(a,b,c,d){this.i=d||Date.now();this.j=a;this.m=b;this.h=c;this.g=void 0};mf.prototype.getMessage=function(){return this.m};
function nf(a,b){this.g=null;this.i=[];this.h=(void 0===b?null:b)||null;this.m=[];this.j={g:function(){return a}}}function of(a){return a.g?a.g:a.h?of(a.h):df}function pf(a,b){for(;a;)a.i.forEach(function(c){c(b)}),a=a.h}function qf(){this.entries={};var a=new nf("");a.g=hf;this.entries[""]=a}var rf;function sf(a,b){var c=a.entries[b];if(c)return c;c=sf(a,b.slice(0,Math.max(b.lastIndexOf("."),0)));var d=new nf(b,c);a.entries[b]=d;c.m.push(d);return d}function tf(){rf||(rf=new qf);return rf}
function uf(a){return sf(tf(),a).j}function vf(a,b,c,d){var e;if(e=a)if(e=a&&b){e=b.value;var f=a?of(sf(tf(),a.g())):df;e=e>=f.value}e&&(b=b||df,e=sf(tf(),a.g()),"function"===typeof c&&(c=c()),lf||(lf=new kf),a=new mf(b,c,a.g()),a.g=d,pf(e,a))}function W(a,b,c){a&&vf(a,ef,b,c)}function X(a,b,c){a&&vf(a,gf,b,c)}function wf(a,b){a&&vf(a,jf,b)};var xf=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function yf(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function zf(a,b,c){c=null!=c?"="+encodeURIComponent(String(c)):"";if(b+=c){c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;a=a[0]+(a[1]?"?"+a[1]:"")+a[2]}return a}function Af(a,b,c){for(;0<=(b=a.indexOf("zx",b))&&b<c;){var d=a.charCodeAt(b-1);if(38==d||63==d)if(d=a.charCodeAt(b+2),!d||61==d||38==d||35==d)return b;b+=3}return-1}var Bf=/#|$/,Cf=/[?&]($|#)/;
function Df(a){for(var b=Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36),c=a.search(Bf),d=0,e,f=[];0<=(e=Af(a,d,c));)f.push(a.substring(d,e)),d=Math.min(a.indexOf("&",e)+1||c,c);f.push(a.slice(d));a=f.join("").replace(Cf,"$1");return zf(a,"zx",b)};function Ef(a,b,c,d,e){R.call(this);var f=this;this.i=uf("apps.common.net.OfflineObserver");vf(this.i,hf,"new apps.common.net.OfflineObserver("+a+")");this.C=new V(this);this.X=a;this.j=new Ye(function(){return Ff(f,!1)},1E4);this.I=c||12E4;this.l=new bf;this.g=b;this.G=Date.now();this.B=d||new Ze;this.F=e||new pd;this.C.listen(this.l,"online",this.D).listen(this.l,"offline",this.D);this.Y=He();Gf(this,!0,!0)}q(Ef,R);
function Gf(a,b,c){var d=Date.now(),e=a.g?25E3:1E4;if(b||!a.g)b=c?0:e,a.G=d-b;else{b=e;c=a.G+e;for(var f=1;c<d&&b<a.I;)b=Math.min(a.I,e*Math.pow(1.2,f++)),c+=b}d=a.B;a=Ja(a.S,a);e=b;md(d.g);d.g=new Ye(a,e);d.g.start()}Ef.prototype.S=function(){Gf(this);Hf(this)};Ef.prototype.D=function(){X(this.i,"OnlineHandler transitioned "+(navigator.onLine?"ONLINE":"OFFLINE"));Gf(this,!0,!0)};function Hf(a){wf(a.i,"Performing image ping.");a.j.stop();a.j.start();qd(a.F,Df(a.X),function(b){return Ff(a,b)})}
function Ff(a,b){wf(a.i,"Image ping "+(b?"succeeded":"failed"));a.j.stop();wf(a.i,"notifyTrustedNetworkResult("+b+")");a.Y.resolve();b!=a.g?(X(a.i,"OfflineObserver transitioned "+(b?"ONLINE":"OFFLINE")),a.g=b,be(a,b?"a":"b"),b=!0):b=!1;Gf(a,b)}Ef.prototype.A=function(){this.j.stop();nd(this.B,this.j,this.l,this.C,this.F);R.prototype.A.call(this)};function If(){this.g=!1}q(If,M);n=If.prototype;n.P=function(){if(!this.g){this.g=!0;this.Z();var a=O(Nc(this));ld(ld(N(a.g.prototype.u[0])+N(kd("[]",a.h)),"."),"$")}};n.ha=function(){return this.g};n.Z=function(){if(this.j){var a,b;for(a=(b=Jf(this.j).g,b());(a.g+1|0)<a.h.length;){b=a;if(!((b.g+1|0)<b.h.length))throw a=new ed,Qc(a),a.h(Error(a)),a.g;var c=b.g=b.g+1|0;b.h[c].P()}this.j.length=0}};n.toString=function(){return M.prototype.toString.call(this)||""};
n.u=["com.google.apps.xplat.disposable.Disposable",0];function Kf(){this.g=0}q(Kf,M);Kf.prototype.u=["com.google.gwt.corp.collections.AbstractJsArray$Iter",0];function Lf(){}function Jf(a){return new bd(function(){var b=new Mf;b.g=-1;b.h=a;return b})}function Nf(a){return a instanceof Array}Lf.prototype.u=["Array",0];function Mf(){this.g=0}q(Mf,Kf);Mf.prototype.u=["com.google.gwt.corp.collections.JsArray$Iter",0];function Of(){}q(Of,M);function Pf(a){return a instanceof Of}Of.prototype.u=["com.google.apps.docsshared.xplat.observable.EventObserverTracker$ObservableObserverPair",0];function Qf(){this.g=!1;this.h=Sc([],Nf,Lf)}q(Qf,If);function Rf(a,b,c){a=a.h;c=b.g(c);var d=new Of;d.g=b;d.h=c;a.push(d)}Qf.prototype.Z=function(){this.removeAll();If.prototype.Z.call(this)};Qf.prototype.removeAll=function(){for(var a=Sc(this.h.pop(),Pf,Of);a;)a.g.i(a.h)&&a.g.h(a.h),a=Sc(this.h.pop(),Pf,Of)};Qf.prototype.u=["com.google.apps.docsshared.xplat.observable.EventObserverTracker",0];function Sf(a,b,c,d){c=void 0===c?[]:c;d=void 0===d?[]:d;P.call(this);var e=this;this.i=b||this;this.j=a;this.h=new Qf;Q(this,this.h);this.g=new V(this);Q(this,this.g);this.l=c;this.s=d;a instanceof Ef?(this.g.listen(a,"a",function(){return Tf(e)}),this.g.listen(a,"b",function(){return Uf(e)})):(Rf(this.h,this.j.da(),function(){return Tf(e)}),Rf(this.h,this.j.ca(),function(){return Uf(e)}))}q(Sf,P);function Vf(a,b){a.l.push(b)}function Tf(a){a.l.forEach(function(b){try{b.call(a.i)}catch(c){}})}
function Uf(a){a.s.forEach(function(b){try{b.call(a.i)}catch(c){}})};function Wf(a){this.o=E(a)}q(Wf,L);function Xf(){R.call(this);this.g=3;this.i=null;this.j=!1;this.l=He()}q(Xf,R);function Yf(a,b,c){if((1==b||2==b)&&!c)throw Error("Eligibility must be provided when transitioning to ENABLED or DISABLED. New state: "+b);c&&(a.i=c);a.g=b;a.l.resolve(null);be(a,"c")}function Zf(a){if(!a.i)return!1;a=F(a.i,1);return 1==a||6==a};function $f(a){this.o=E(a)}q($f,L);$f.prototype.getTitle=function(){return uc(K(this,2),"")};$f.prototype.setTitle=function(a){return tc(this,2,a)};function ag(a){this.o=E(a)}q(ag,L);function bg(a){var b=new ag;return tc(b,1,a)};function cg(a,b){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);this.ta=b}q(cg,Error);function dg(a,b,c){sd.call(this,"broadcast-message",a);this.h=b;this.data=c}q(dg,sd);function eg(a){this.o=E(a,1)}q(eg,L);function fg(a){this.o=E(a)}q(fg,L);fg.prototype.getMessage=function(){return J(this,eg,2)};function gg(a,b){R.call(this);this.j=!1;this.g=null;this.i=new V(this);Q(this,this.i);this.l=a;this.B=b}q(gg,R);gg.prototype.connect=function(){this.j||(this.j=!0,this.g=this.l.g,this.i.listen(this.g,"message",this.C.bind(this)),this.g.start())};gg.prototype.C=function(a){var b=a.h;null!=b.data[1]?(b=new fg(b.data),a=uc(K(b,3),""),b=b.getMessage()):(a=this.B,b=new eg(b.data));be(this,new dg(this,a,b))};gg.prototype.A=function(){if(this.g){var a=new fg;a=G(a,1,0);this.g.postMessage(bc(a));this.g.close()}R.prototype.A.call(this)};function hg(a,b,c){P.call(this);this.l=a;this.i=b;this.j=c||null;this.g=[];this.h=new V(this);Q(this,this.h);this.h.listen(this.l,"broadcast-message",this.s)}q(hg,P);hg.prototype.listen=function(a,b){this.g.push(new ig(a,b));return this};hg.prototype.s=function(a){for(var b=0;b<this.g.length;b++){var c=this.g[b];if(!this.i||this.i==a.h){var d=a.data,e=c.g;d=e.g?e.i(d,e.g,e.h,!0):e.i(d,e.h,e.defaultValue,!0);null!=d&&c.M.call(this.j,d,a.h)}}};function ig(a,b){this.g=a;this.M=b};function jg(a){this.o=E(a)}q(jg,L);var kg=new xc(102041228,jg);function lg(a){this.o=E(a)}q(lg,L);var mg=new xc(108529910,lg);function ng(a){this.o=E(a)}q(ng,L);var og=new xc(122453513,ng);function pg(a){this.o=E(a)}q(pg,L);function qg(a){this.o=E(a)}q(qg,L);function rg(a,b){P.call(this);this.j=a;this.B=this.j.g;this.K=b;this.D=new Sf(this.j,this,[this.F],[this.G]);Q(this,this.D);this.C=this.i=0;this.h=this.l=2E4;this.g=new Ye(this.I,0,this);Q(this,this.g);this.s=!1}q(rg,P);function sg(a){if(15<=a.i)return a.g.stop(),!1;if(!a.B&&!a.j.g&&3<=a.C)return!0;tg(a);return!0}function tg(a){15<=a.i||a.s||a.g.isActive()||a.g.start(a.h)}
rg.prototype.I=function(){this.K();this.B||this.j.g||this.C++;this.i++;144E5!=this.h&&(this.h=Math.min(this.h+(50+50*Math.random())/100*this.l,144E5),this.l*=2)};rg.prototype.F=function(){15<=this.i||(this.h=2E4*Math.random(),this.l=2E4,tg(this))};rg.prototype.G=function(){this.g.stop()};function ug(a,b,c,d,e,f,g,h,k,l){k=void 0===k?!1:k;l=void 0===l?!1:l;P.call(this);var m=this;this.i=uf("docs.offline.clients.BaseOfflineManager");this.g=a;this.I=He();vg(this);this.B=this.s=null;this.X=g;this.G=new Sf(g,this);Q(this,this.G);this.h=b;this.K=h||null;this.S=!1;this.j=null;k&&(this.j=new rg(this.X,function(){return wg(m)}),Q(this,this.j));this.C=He();this.D=He();this.F=new V(this);Q(this,this.F);this.ka="user_"+c;this.ca=d;this.ja=e;this.da=f;this.la=!1;this.l=l?new Ve(25E3):null;l&&
this.F.listen(this.l,"tick",function(){return xg(m)})}q(ug,P);n=ug.prototype;n.start=function(){var a=this;this.S||(this.S=!0,yg(this).then(function(){a.C.resolve();if(a.j){var b=a.j;b.g.stop();b.s=!0}},function(b){b=td(b);var c=new cg(b.message,a.j?a.D.promise:void 0);vd(c,{originalStackTrace:b.stack});vd(c,{offlineManagerInitializationFailure:!0,isRetry:!1});a.C.reject(c);a.j?sg(a.j):a.h.l.resolve(c)}));return this.C.promise};
function yg(a){X(a.i,"Starting offline manager");return zg(a.g).then(function(){return Ag(a.g)}).then(function(b){b.connect();a.I.resolve(b);return Bg(a)}).then(function(){Vf(a.G,function(){Cg(a)});a.la=!0})}function wg(a){a.g.reset();U(yg(a).then(function(){var b=a.j;b.g.stop();b.s=!0;a.D.resolve(a.j.i)}),function(){if(!sg(a.j)){var b=vd("OfflineService start retries exhausted",{offlineManagerInitializationFailure:!0,isRetry:!0});a.D.reject(b)}})}
function Dg(a){if(2!=a.h.g)throw Error("Cannot enable offline when it is not disabled. Current state:"+a.h.g);X(a.i,"Enabling offline.");Yf(a.h,4);return Eg(a.g,12).then(function(b){var c=F(b,1);return Ge().then(function(){return 1==c||2==c?Fg(a,1):Gg(a)}).then(function(){return b})})}
function Bg(a){return Hg(a.g,6E4).then(function(b){switch(F(b,1)){case 1:return U(Ig(a.g),function(c){a.K&&a.K.g(td(c))}).then(function(){return Fg(a,1)}).then(function(){Cg(a)});case 2:return Gg(a);case 4:return X(a.i,"Offline is enabled for another user: "+K(b,2)),X(a.i,"Offline is disabled."),Fg(a,2);case 3:return b=F(b,3),null!=b?Jg(a,b).then(function(c){return c?Fg(a,1):Gg(a)}):Gg(a);case 5:return Kg(a).then(function(c){return c?Fg(a,1):Gg(a)})}})}
function Gg(a){X(a.i,"Offline is disabled.");return Fg(a,2)}n.qa=function(a){var b=this;1==this.h.g&&!this.h.j&&0==F(a,2)&&U(Hg(this.g).then(function(c){var d=b.h;d.j=jc(c,5);be(d,"c")}),function(c){W(b.i,"Error querying for offline state after task finished event.",c)})};function Jg(a,b){if(!a.ca)return Ge(!1);X(a.i,"Offline is not enabled. Trying to auto enable with reason: "+b);return Eg(a.g,b).then(function(c){switch(F(c,1)){case 1:return!0;case 2:return!0;default:return!1}})}
function Kg(a){if(!a.ja)return Ge(!1);X(a.i,"Trying to recover offline due to db corruption");return Eg(a.g,2).then(function(b){switch(F(b,1)){case 1:case 2:return!0;default:return!1}})}function vg(a){var b;a.I.promise.then(function(c){b=c;return a.ga()}).then(function(c){a.s=new hg(b,c,a);Q(a,a.s);a.B=new hg(b,null,a);Q(a,a.B);a.s.listen(mg,a.pa).listen(kg,a.oa).listen(og,a.qa);a.B.listen(mg,a.na).listen(kg,a.ma)})}
n.pa=function(){var a=this;U(Fg(this,1),function(b){X(a.i,"error in handle offline enabled from event bus",b)})};n.oa=function(){var a=this;U(Gg(this),function(b){X(a.i,"error in handle offline disabled from event bus",b)})};n.na=function(){Lg(this)};n.ma=function(){Lg(this)};
function Fg(a,b){return Mg(a.g).then(function(c){Yf(a.h,b,c)}).then(function(){return Hg(a.g).then(function(c){var d=a.h;d.j=!!jc(c,5);be(d,"c")})}).then(function(){if(a.l)switch(b){case 1:a.l.start();xg(a);break;case 2:a.l.stop()}})}function Ng(a,b,c){return Og(a.g,b,c).then(function(){})}function Lg(a){Mg(a.g).then(function(b){var c=a.h;c.i=b;be(c,"c")})}function Cg(a){a.da&&1==a.h.g&&U(Pg(a.g),function(b){W(a.i,"Error executing pending tasks",b)})}n.ga=function(){return Ge(this.ka)};
function xg(a){U(Qg(a.g),function(b){W(a.i,"Got an error so stopping the SW heartbeat.",td(b));a.l&&a.l.stop()})};function Rg(a,b,c){P.call(this);this.s=null!=c?a.bind(c):a;this.l=b;this.j=null;this.h=!1;this.i=0;this.g=null}q(Rg,P);Rg.prototype.stop=function(){this.g&&(u.clearTimeout(this.g),this.g=null,this.h=!1,this.j=null)};Rg.prototype.pause=function(){this.i++};Rg.prototype.resume=function(){this.i--;this.i||!this.h||this.g||(this.h=!1,Sg(this))};Rg.prototype.A=function(){P.prototype.A.call(this);this.stop()};
function Sg(a){a.g=We(function(){a.g=null;a.h&&!a.i&&(a.h=!1,Sg(a))},a.l);var b=a.j;a.j=null;a.s.apply(null,b)};function Tg(){}Tg.prototype.next=function(){return Ug};var Ug={done:!0,value:void 0};Tg.prototype.aa=function(){return this};function Vg(a){if(a instanceof Tg)return a;if("function"==typeof a.aa)return a.aa(!1);if(Fa(a)){var b=0,c=new Tg;c.next=function(){for(;;){if(b>=a.length)return Ug;if(b in a)return{value:a[b++],done:!1};b++}};return c}throw Error("Not implemented");};function Wg(a){this.g={};if(a)for(var b=0;b<a.length;b++)this.g[Xg(a[b])]=null;for(var c in Object.prototype);}var Yg={};function Xg(a){return a in Yg||32==String(a).charCodeAt(0)?" "+a:a}function Zg(a){return 32==a.charCodeAt(0)?a.slice(1):a}n=Wg.prototype;n.add=function(a){this.g[Xg(a)]=null};n.clear=function(){this.g={}};n.contains=function(a){return Xg(a)in this.g};n.has=function(a){return this.contains(a)};n.forEach=function(a,b){for(var c in this.g)a.call(b,Zg(c),void 0,this)};
n.values=Object.keys?function(){return Object.keys(this.g).map(Zg,this)}:function(){var a=[],b;for(b in this.g)a.push(Zg(b));return a};n.R=function(){return this.values()};n.remove=function(a){a=Xg(a);a in this.g?(delete this.g[a],a=!0):a=!1;return a};n.aa=function(){return Vg(this.R())};function $g(a,b,c){R.call(this);this.g=uf("docs.offline.clients.DocumentAvailabilityTracker");this.l=b;this.i=new Wg;this.j=new Wg;this.C=new Rg(this.B,void 0!=c?c:3E3,this);Q(this,this.C)}q($g,R);
function ah(a,b){0!=b.length&&U(bh(a.l,b).then(function(c){for(var d=[],e=new Wg(b),f=0;f<c.length;f++){var g=c[f],h=K(g,1);uc(jc(g,8),!1)&&(d.push(h),e.remove(h))}c=a.i;for(f=0;f<d.length;f++)c.g[Xg(d[f])]=null;d=a.i;for(var k in e.g)delete d.g[k];be(a,"d");wf(a.g,"Updated cache based on "+b.length+" document updates.")}),function(c){c=new ch("Failed to process document updates: "+b.length+" documents.",td(c));W(a.g,"DocumentAvailabilityTracker encountered background error:",c)})}
$g.prototype.B=function(){var a=this.j.R();this.j.clear();ah(this,a)};function ch(a){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack)}q(ch,Error);function dh(a){this.g=a};function eh(a){this.o=E(a)}q(eh,L);function fh(a,b){var c=a.o,d=D(c);Rb(d);if(null==b)H(c,d,1);else{if(!(B(b)&4)){Object.isFrozen(b)&&(b=z(b));for(var e=0;e<b.length;e++){var f=b,g=e,h=b[e];if("string"!==typeof h)throw Error();f[g]=h}C(b,5)}H(c,d,1,b)}return a}eh.N=[1];function gh(a){this.o=E(a)}q(gh,L);function hh(a,b){return G(a,1,b)};function ih(a){this.o=E(a)}q(ih,L);function jh(a,b){return qc(a,1,b)}function kh(a,b){return sc(a,2,b)}function lh(a,b){return sc(a,3,b)}ih.N=[1];function mh(a){this.o=E(a)}q(mh,L);function Y(a){var b=new mh;return G(b,1,a)};function nh(a){this.o=E(a)}q(nh,L);nh.N=[1];function oh(a){this.o=E(a)}q(oh,L);function ph(a){this.o=E(a)}q(ph,L);function qh(a){this.o=E(a)}q(qh,L);function rh(a){this.o=E(a)}q(rh,L);function sh(a){this.o=E(a)}q(sh,L);function th(a){this.o=E(a)}q(th,L);function uh(a){this.o=E(a)}q(uh,L);function vh(a){this.i=this.s=this.j="";this.l=null;this.m=this.h="";this.v=!1;var b;a instanceof vh?(this.v=a.v,wh(this,a.j),this.s=a.s,xh(this,a.i),yh(this,a.l),this.h=a.h,zh(this,Ah(a.g)),this.m=a.m):a&&(b=String(a).match(xf))?(this.v=!1,wh(this,b[1]||"",!0),this.s=Bh(b[2]||""),xh(this,b[3]||"",!0),yh(this,b[4]),this.h=Bh(b[5]||"",!0),zh(this,b[6]||"",!0),this.m=Bh(b[7]||"")):(this.v=!1,this.g=new Ch(null,this.v))}
vh.prototype.toString=function(){var a=[],b=this.j;b&&a.push(Dh(b,Eh,!0),":");var c=this.i;if(c||"file"==b)a.push("//"),(b=this.s)&&a.push(Dh(b,Eh,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.l,null!=c&&a.push(":",String(c));if(c=this.h)this.i&&"/"!=c.charAt(0)&&a.push("/"),a.push(Dh(c,"/"==c.charAt(0)?Fh:Gh,!0));(c=this.g.toString())&&a.push("?",c);(c=this.m)&&a.push("#",Dh(c,Hh));return a.join("")};
vh.prototype.resolve=function(a){var b=new vh(this),c=!!a.j;c?wh(b,a.j):c=!!a.s;c?b.s=a.s:c=!!a.i;c?xh(b,a.i):c=null!=a.l;var d=a.h;if(c)yh(b,a.l);else if(c=!!a.h){if("/"!=d.charAt(0))if(this.i&&!this.h)d="/"+d;else{var e=b.h.lastIndexOf("/");-1!=e&&(d=b.h.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==f.length&&
""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.h=d:c=""!==a.g.toString();c?zh(b,Ah(a.g)):c=!!a.m;c&&(b.m=a.m);return b};function wh(a,b,c){a.j=c?Bh(b,!0):b;a.j&&(a.j=a.j.replace(/:$/,""));return a}function xh(a,b,c){a.i=c?Bh(b,!0):b;return a}function yh(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.l=b}else a.l=null;return a}function zh(a,b,c){b instanceof Ch?(a.g=b,Ih(a.g,a.v)):(c||(b=Dh(b,Jh)),a.g=new Ch(b,a.v))}
function Bh(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function Dh(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,Kh),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function Kh(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var Eh=/[#\/\?@]/g,Gh=/[#\?:]/g,Fh=/[#\?]/g,Jh=/[#\?@]/g,Hh=/#/g;function Ch(a,b){this.h=this.g=null;this.i=a||null;this.j=!!b}
function Lh(a){a.g||(a.g=new Map,a.h=0,a.i&&yf(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}n=Ch.prototype;n.add=function(a,b){Lh(this);this.i=null;a=Mh(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};n.remove=function(a){Lh(this);a=Mh(this,a);return this.g.has(a)?(this.i=null,this.h-=this.g.get(a).length,this.g.delete(a)):!1};n.clear=function(){this.g=this.i=null;this.h=0};function Nh(a,b){Lh(a);b=Mh(a,b);return a.g.has(b)}
n.forEach=function(a,b){Lh(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};n.R=function(a){Lh(this);var b=[];if("string"===typeof a)Nh(this,a)&&(b=b.concat(this.g.get(Mh(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};n.set=function(a,b){Lh(this);this.i=null;a=Mh(this,a);Nh(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};
n.get=function(a,b){if(!a)return b;a=this.R(a);return 0<a.length?String(a[0]):b};n.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.R(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.i=a.join("&")};function Ah(a){var b=new Ch;b.i=a.i;a.g&&(b.g=new Map(a.g),b.h=a.h);return b}
function Mh(a,b){b=String(b);a.j&&(b=b.toLowerCase());return b}function Ih(a,b){b&&!a.j&&(Lh(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(this.remove(d),this.remove(e),0<c.length&&(this.i=null,this.g.set(Mh(this,e),hb(c)),this.h+=c.length))},a));a.j=b};function Oh(a){return(a=a.exec(Wa()))?a[1]:""}
(function(){if(vb)return Oh(/Firefox\/([0-9.]+)/);if(lb||mb||kb)return ub;if(zb){if(eb()||v("iPad")||v("iPod")||(Qa&&Xa&&Xa.platform?"macOS"===Xa.platform:v("Macintosh"))){var a=Oh(/CriOS\/([0-9.]+)/);if(a)return a}return Oh(/Chrome\/([0-9.]+)/)}if(Ab&&!(eb()||v("iPad")||v("iPod")))return Oh(/Version\/([0-9.]+)/);if(wb||xb){if(a=/Version\/(\S+).*Mobile\/(\S+)/.exec(Wa()))return a[1]+"."+a[2]}else if(yb)return(a=Oh(/Android\s+([0-9.]+)/))?a:Oh(/Version\/([0-9.]+)/);return""})();/*

 SPDX-License-Identifier: Apache-2.0
*/
function Ph(a,b,c,d,e,f){e=void 0===e?null:e;P.call(this);this.h=He();f||(f=Oa||(Oa=new qe));this.j=f;this.i=uf("docs.offline.iframeapi.IframeClientImpl");this.l=new V(this);Q(this,this.l);this.C=a;this.G=d;a=new vh(b);a.g.set("ouid",this.C);f=new vh(u.location.href);"true"!=f.g.get("Debug")&&"true"!=f.g.get("debug")&&"pretty"!=f.g.get("debug")&&"DU"!=f.g.get("jsmode")||a.g.set("Debug","true");f=[];e&&f.push("csid="+e);f.push("cd="+c);a.m=f.join("&");if(/^[\s\xa0]*$/.test(a.i))throw Error("Url contains invalid domain: "+
b);this.D=yh(xh(wh(new vh,a.j),a.i),a.l).toString();b=zf(a.toString(),"sa",""+d);this.F=ke(null===b?"null":void 0===b?"undefined":b);this.g=null;this.s=!1}q(Ph,P);function zg(a){a.l.listen(ne(a.j.g),"message",a.B);Qh(a);Xe().then(function(){a.s||(Rh(a),Sh(a),a.h.reject(Error("Iframe initialization timed out")))});return a.h.promise.then()}Ph.prototype.reset=function(){Sh(this);this.h=He()};function Qg(a){var b=Y(12);return Z(a,b).then(function(){})}
function Ag(a){var b=Y(1);return Z(a,b).then(function(c){c=new gg(new dh(c.port),"user_"+a.C);Q(a,c);return c})}function bh(a,b){var c=Y(2);b=fh(new eh,b);oc(c,2,b);return Z(a,c).then(function(d){return nc(J(d.g,nh,3),$f,1)})}function Th(a){var b=Y(3);return Z(a,b).then(function(c){return nc(J(c.g,nh,3),$f,1)})}function Hg(a,b){var c=Y(4);return Z(a,c,b).then(function(d){return J(J(d.g,sh,4),qg,1)})}function Mg(a){var b=Y(10);return Z(a,b).then(function(c){return J(J(c.g,rh,7),Wf,1)})}
function Og(a,b,c){var d=void 0===d?!1:d;var e=Y(5);b=lh(kh(jh(new ih,b),c),d);oc(e,3,b);return Z(a,e).then(function(f){return(f=J(f.g,th,8))&&J(f,pg,1)||null})}function Eg(a,b){var c=Y(6);b=hh(new gh,b);oc(c,4,b);return Z(a,c).then(function(d){return J(J(d.g,oh,5),Wf,1)})}function Ig(a){var b=Y(9);return Z(a,b).then(function(){})}function Pg(a){var b=Y(11);return Z(a,b).then(function(){})}function Uh(a){var b=Y(13);return Z(a,b).then(function(c){return K(J(c.g,qh,9),1)})}
function Z(a,b,c){c=void 0===c?3E4:c;return U(a.h.promise.then(function(d){X(a.i,"Sending message to the docs offline iframe api. Type:"+F(b,1));var e=new MessageChannel;d.postMessage(bc(b),[e.port2]);var f=He();e.port1.onmessage=function(h){h=new Vh(h);h.g&&J(h.g,ph,2)?(h=J(h.g,ph,2),f.reject(Error("Iframe api request of type "+F(b,1)+" failed (error type "+F(h,1)+"): "+K(h,2)))):f.resolve(h)};var g=We(function(){f.reject(Error("Iframe api request of type "+F(b,1)+" timed out."))},c);Ke(f.promise,
function(){e.port1.close();u.clearTimeout(g)});return f.promise}),function(d){throw vd(d,{iframeRequest_docsExtensionManifestVersion:String(u._docs_chrome_extension_manifest_version||2),iframeRequest_sourceApplication:a.G.toString(),iframeRequest_requestType:uc(F(b,1),0).toString()});})}
Ph.prototype.B=function(a){(a=a.h)&&a.origin==this.D&&a.source==this.g.contentWindow&&(this.s?W(this.i,"Docs offline iframe already connected to this client."):(a.ports&&a.ports.length&&a.ports[0]?(a=a.ports[0],od(this,a.close,a),this.h.resolve(a),this.s=!0,X(this.i,"Docs Offline iframe connected to this client.")):W(this.i,"Docs offline frame api sent a message with no port."),Rh(this)))};function Rh(a){af(a.l,ne(a.j.g),"message",a.B)}
function Qh(a){a.g=a.j.h("IFRAME",{style:"display:none"});a.g.src=ie(a.F).toString();a.j.g.body.appendChild(a.g)}function Sh(a){if(a.g){var b=a.g;b&&b.parentNode&&b.parentNode.removeChild(b);a.g=null}}Ph.prototype.A=function(){Sh(this);P.prototype.A.call(this)};function Vh(a){this.g=a.data?new uh(a.data):null;this.port=a.ports&&a.ports.length&&a.ports[0]?a.ports[0]:null};function Wh(a,b,c,d,e){b=new Ph(a,ie(b).toString(),c,d);c=new Xf;ug.call(this,b,c,a,!1,!1,!0,e,void 0,!0);Q(this,b);Q(this,c);this.Y=new $g(c,b);Q(this,this.Y)}q(Wh,ug);Wh.prototype.ga=function(){return Uh(this.g)};var Xh=ha(["https://docs.google.com/offline/iframeapi"]),Yh=uf("switchblade.offline.client"),Zh=function(a){var b=Ba.apply(1,arguments);if(0===b.length)return ke(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return ke(c)}(Xh),$h=new Map([["application/vnd.google-apps.document","kix"],["application/vnd.google-apps.drawing","drawing"],["application/vnd.google-apps.presentation","punch"],["application/vnd.google-apps.spreadsheet","ritz"]]);
function ai(){var a=void 0===a?Zh:a;this.h=this.g=null;this.i=a}
ai.prototype.onMessage=function(a,b){var c=this,d,e,f,g,h;return Aa(function(k){switch(k.g){case 1:try{d=new Ic(a)}catch(m){return b.disconnect(),k.return()}e=K(d,2);if(!e)return b.disconnect(),k.return();k.h=2;return r(k,bi(c,e),4);case 4:sa(k,3);break;case 2:return f=ta(k),W(Yh,"Failed to connect to offline service: "+f),b.disconnect(),k.return();case 3:return k.h=5,r(k,ci(c,d),7);case 7:g=k.m;tc(g,2,e);if(null!=rc(d)){var l=rc(d);if(null!=l){if(!Sb(l))throw Error("Expected an int64 value encoded as a number or a string but got "+
l+" a "+Ea(l));l="string"===typeof l?l:l}G(g,1,l)}b.postMessage(bc(g));sa(k,0);break;case 5:h=ta(k),W(Yh,"Failed to handle message: "+h),b.disconnect(),k.g=0}})};
function bi(a,b){var c,d,e;return Aa(function(f){switch(f.g){case 1:if(a.g)return f.return();a.h=new Ef("https://ssl.gstatic.com/docs/common/netcheck.gif",!0);a.g=new Wh(b,a.i,4,11,a.h);f.h=2;return r(f,a.g.start(),4);case 4:sa(f,0);break;case 2:c=ta(f);W(Yh,"DocsOffline initialization error: "+c);d=c.ta;if(!d)throw c;f.h=5;return r(f,d,7);case 7:sa(f,0);break;case 5:throw e=ta(f),e;}})}
function ci(a,b){var c,d,e;return Aa(function(f){if(1==f.g){if(7===kc(b))return f.return(di(a,b));c=a.g.h;1==c.g?(f.g=2,f=void 0):f=Zf(c)?f.return(Promise.resolve(Jc(23))):r(f,Mg(a.g.g),3);return f}if(2!=f.g)switch(d=f.m,e=F(d,1),e){case 1:case 2:case 6:return f.return(Promise.reject(Error("Invalid eligibility reason: "+e)));case 0:return f.return(Promise.resolve(Jc(22)));case 3:return f.return(Promise.resolve(Jc(100)));case 4:return f.return(Promise.resolve(Jc(17)));case 5:return f.return(Promise.resolve(Jc(36)));
case 7:return f.return(Promise.resolve(Jc(38)))}switch(kc(b)){case 4:return f.return(ei(a,b));case 6:return f.return(fi(a,b));default:return f.return(Promise.reject(Error("Unhandled message case: "+kc(b))))}})}
function gi(a,b){var c,d;return Aa(function(e){switch(e.g){case 1:return e.h=2,0<b.length?r(e,bh(a.g.g,b),7):r(e,Th(a.g.g),6);case 6:c=e.m;e.g=5;break;case 7:c=e.m;case 5:sa(e,3);break;case 2:return ta(e),e.return(Promise.resolve(Kc(new Ic,Gc(new Fc,!1))));case 3:return d=[],c.forEach(function(f){if(0<b.length||uc(jc(f,7),!1)||uc(jc(f,8),!1)){var g=uc(jc(f,7),!1)?2:3,h=d,k=h.push,l=new yc;g=G(l,2,g);g=sc(g,5,uc(jc(f,8),!1));f=K(f,1);f=tc(g,1,null==f?void 0:f);k.call(h,f)}}),e.return(Promise.resolve(Kc(new Ic,
Gc(Hc(new Fc,Ec(new Dc,d)),!0))))}})}
function di(a,b){var c,d,e,f;return Aa(function(g){switch(g.g){case 1:c=new Ac;d=a.g.h;if(!jc(vc(b,zc,7),2)){c.setEnabled(1==d.g);G(c,3,1);g.g=2;break}if(1==d.g){c.setEnabled(!0);G(c,3,3);G(c,4,2);g.g=2;break}if(!Zf(d)){c.setEnabled(!1);if(2==d.g){G(c,3,2);var h=F(d.i,1);G(c,4,null==h?void 0:h)}else G(c,3,0);g.g=2;break}g.h=5;return r(g,Dg(a.g),7);case 7:e=g.m;f=F(e,1);1===f?c.setEnabled(!0):(2===f?(c.setEnabled(!0),G(c,3,3)):(c.setEnabled(!1),G(c,3,2)),G(c,4,f));sa(g,2);break;case 5:ta(g),c.setEnabled(1==
d.g),G(c,3,0);case 2:h=g.return;var k=Promise,l=k.resolve;var m=new Ic;m=pc(m,8,c);return h.call(g,l.call(k,m))}})}function ei(a,b){var c;return Aa(function(d){c=vc(b,Cc,4);var e=d.return,f=c.o,g=D(f),h=g&2,k=ic(f,g,3),l=B(k);if(!(l&4)){Object.isFrozen(k)&&(l=0,k=z(k),H(f,g,3,k));for(var m=0,x=0;m<k.length;m++){var w=Tb(k[m]);null!=w&&(k[x++]=w)}x<m&&(k.length=x);l|=5;h&&(l|=34);C(k,l);l&2&&Object.freeze(k)}!h&&(l&2||Object.isFrozen(k))&&(k=z(k),Fb(k,5),H(f,g,3,k));return e.call(d,gi(a,k))})}
function fi(a,b){var c,d,e,f,g,h,k,l,m,x,w;return Aa(function(y){switch(y.g){case 1:c=[];d=[];e=[];f=ia(nc(vc(b,Bc,6),yc,3));for(g=f.next();!g.done;g=f.next()){h=g.value;k=K(h,1);l=F(h,2);var I=K(h,4);m=I?$h.has(I)?$h.get(I):"unknown":"unknown";if(2===l){I=d;var aa=I.push;var T=bg(k);T=tc(T,2,m);aa.call(I,T)}else 3===l&&(I=e,aa=I.push,T=bg(k),T=tc(T,2,m),aa.call(I,T));c.push(k)}x=[];0<d.length&&x.push(Ng(a.g,d,!0));0<e.length&&x.push(Ng(a.g,e,!1));y.h=2;return r(y,Promise.all(x),4);case 4:sa(y,3);
break;case 2:return w=ta(y),W(Yh,"Failed to change pin state. "+w),y.return(Promise.resolve(Kc(new Ic,Gc(new Fc,!1))));case 3:return y.return(gi(a,c))}})};var hi=uf("switchblade.offline.offscreen");function ii(a){if("com.google.drive.offscreenproxy"===a.name){a.onDisconnect.addListener(function(){var c,d=(null==(c=chrome.runtime.lastError)?void 0:c.message)||"";wf(hi,"Extension message port disconnected: "+d)});var b=new ai;a.onMessage.addListener(function(c){wf(hi,"Offscreen request: "+JSON.stringify(c));b.onMessage(c,a)})}};function ji(){this.g=Date.now()}var ki=null;ji.prototype.set=function(a){this.g=a};ji.prototype.reset=function(){this.set(Date.now())};ji.prototype.get=function(){return this.g};function li(a){this.j=a||"";ki||(ki=new ji);this.m=ki}li.prototype.g=!0;li.prototype.h=!0;li.prototype.i=!1;function mi(a){return 10>a?"0"+a:String(a)}function ni(a){li.call(this,a)}La(ni,li);
function oi(a,b){var c=[];c.push(a.j," ");if(a.h){var d=new Date(b.i);c.push("[",mi(d.getFullYear()-2E3)+mi(d.getMonth()+1)+mi(d.getDate())+" "+mi(d.getHours())+":"+mi(d.getMinutes())+":"+mi(d.getSeconds())+"."+mi(Math.floor(d.getMilliseconds()/10)),"] ")}d=c.push;var e=a.m.get();e=(b.i-e)/1E3;var f=e.toFixed(3),g=0;if(1>e)g=2;else for(;100>e;)g++,e*=10;for(;0<g--;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.h,"] ");c.push(b.getMessage());a.i&&(b=b.g,void 0!==b&&c.push("\n",b instanceof Error?b.message:
String(b)));a.g&&c.push("\n");return c.join("")};function pi(){this.m=Ja(this.i,this);this.g=new ni;this.g.h=!1;this.g.i=!1;this.h=this.g.g=!1;this.j={}}pi.prototype.i=function(a){function b(f){if(f){if(f.value>=ef.value)return"error";if(f.value>=ff.value)return"warn";if(f.value>=hf.value)return"log"}return"debug"}if(!this.j[a.h]){var c=oi(this.g,a),d=qi;if(d){var e=b(a.j);ri(d,e,c,a.g)}}};var qi=u.console;function ri(a,b,c,d){if(a[b])a[b](c,void 0===d?"":d);else a.log(c,void 0===d?"":d)};var si=uf("switchblade.offline");si&&(sf(tf(),si.g()).g=gf);var ti=new pi;if(1!=ti.h){var ui=sf(tf(),"").j,vi=ti.m;ui&&sf(tf(),ui.g()).i.push(vi);ti.h=!0}chrome.runtime.onConnect.addListener(function(a){ii(a)});
