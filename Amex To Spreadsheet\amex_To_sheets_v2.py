#Added "Amex" into the 3rd column. (see formatted_line)

import os

def format_transactions_from_file(input_file_path, output_file_path, blacklist):
    # Read the raw input text from the file
    with open(input_file_path, 'r') as file:
        raw_text = file.read()

    # Split the input into lines and filter out empty lines
    lines = [line.strip() for line in raw_text.strip().split("\n") if line.strip()]

    # Filter out lines containing any blacklist terms
    lines_filtered = []
    for line in lines:
        if not any(blacklist_word in line for blacklist_word in blacklist):
            lines_filtered.append(line)

    # Process the filtered text to format it
    formatted_lines = []
    for i in range(0, len(lines_filtered), 3):
        # In case the structure slightly varies, adjust the slicing accordingly
        date, description, value = lines_filtered[i:i+3]
        month, day = date.split(" ")
        value = f"-{value.replace('$', '')}"  # Convert the value to negative and remove the '$'
        formatted_line = f"{month.upper()}\t{day}\tAmex\t{description}\t\t\t{value}"
        formatted_lines.append(formatted_line)

    formatted_output = "\n".join(formatted_lines)

    # Write the formatted output to the output file
    with open(output_file_path, 'w') as file:
        file.write(formatted_output)

    print(f"Formatted transactions have been written to {output_file_path}")

# Define the blacklist
blacklist = ["Credit", "2X Miles", "Pending"]

# Paths setup
main_path = r"C:\Users\<USER>\Desktop"
input_file_path = os.path.join(main_path, "Input.txt")
output_file_path = os.path.join(main_path, "Output.txt")

# Call the function with the updated paths and blacklist
format_transactions_from_file(input_file_path, output_file_path, blacklist)