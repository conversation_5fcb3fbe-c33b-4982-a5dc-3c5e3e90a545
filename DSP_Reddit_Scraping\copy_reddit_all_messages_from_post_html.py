import re
from bs4 import BeautifulSoup
from datetime import datetime, <PERSON><PERSON><PERSON>

def convert_to_edt(utc_time_str):
    """Converts a UTC timestamp string to a formatted EDT string."""
    utc_time = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
    edt_time = utc_time - timedelta(hours=4)
    return edt_time.strftime('%Y%m%d%H%M')

def extract_content_with_images(element):
    """Extract text content and convert images to markdown links."""
    if not element:
        return ""

    content_parts = []

    # Process all child elements
    for child in element.descendants:
        if child.name == 'img':
            # Convert images to markdown links
            img_src = child.get('src', '')
            img_alt = child.get('alt', 'Image')
            if img_src:
                content_parts.append(f"[{img_alt}]({img_src})")
        elif child.string and child.string.strip():
            # Add text content
            content_parts.append(child.string.strip())

    return ' '.join(content_parts)

def extract_reddit_post_and_messages(html_content):
    soup = BeautifulSoup(html_content, 'lxml')

    # --- Extract Post Information ---
    post_element = soup.find('shreddit-post')
    if not post_element:
        return "No post found."

    post_title = post_element.get('post-title', 'No Title')
    post_url_slug = post_element.get('permalink', '')
    post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
    post_url = f"https://www.reddit.com{post_url_match.group(1)}" if post_url_match else ""

    created_timestamp_str = post_element.get('created-timestamp', '')
    formatted_post_time = convert_to_edt(created_timestamp_str) if created_timestamp_str else ""

    # Extract main post content
    post_author = post_element.get('author', 'Unknown Author')

    # Look for post content in various possible locations
    post_content = ""

    # Try to find post content div
    content_selectors = [
        'div[slot="text-body"]',
        'div[data-testid="post-content"]',
        '.Post-body',
        '[data-click-id="text"]'
    ]

    for selector in content_selectors:
        content_div = post_element.select_one(selector)
        if content_div:
            post_content = extract_content_with_images(content_div)
            break

    # If no content found, try looking in the broader HTML
    if not post_content:
        # Look for post content in the page
        post_content_div = soup.find('div', {'data-testid': 'post-content'})
        if post_content_div:
            post_content = extract_content_with_images(post_content_div)

    # Start building output with main post
    output = ["# Main Post"]
    output.append(f"**Title:** {post_title}")
    output.append(f"**Author:** {post_author}")
    output.append(f"**Time:** {formatted_post_time}")
    output.append(f"**URL:** {post_url}")

    if post_content:
        output.append(f"**Content:** {post_content}")

    output.append("")  # Empty line
    output.append("# Messages")

    # --- Extract Message Information ---
    comments = soup.find_all('shreddit-comment')
    comment_timestamps = {comment['thingid']: convert_to_edt(comment.find('faceplate-timeago')['ts']) for comment in comments if comment.has_attr('thingid') and comment.find('faceplate-timeago')}

    for comment in comments:
        author = comment.get('author', 'Unknown Author')

        time_tag = comment.find('faceplate-timeago')
        timestamp_str = time_tag['ts'] if time_tag else ''
        formatted_time = convert_to_edt(timestamp_str) if timestamp_str else ''

        # Extract message content with images
        content_div = comment.find('div', id=re.compile(r't1_.*-post-rtjson-content'))
        if content_div:
            # Use the new function to handle images and text
            message_content = extract_content_with_images(content_div)
        else:
            message_content = ""

        # Check for replies
        reply_info = ''
        parent_comment_div = comment.find_parent('shreddit-comment')
        if parent_comment_div and parent_comment_div.has_attr('thingid'):
            parent_id = parent_comment_div['thingid']
            reply_ts = comment_timestamps.get(parent_id)
            if reply_ts:
                reply_info = f" - (reply: {reply_ts})"

        output.append(f"- {formatted_time} - [{author}] - {message_content}{reply_info}")

    return "\n".join(output)

import pyperclip

def save_debug_html(html_content, filename="debug_reddit_post.html"):
    """Save HTML content to file for debugging."""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"Debug HTML saved to {filename}")
    except Exception as e:
        print(f"Could not save debug HTML: {e}")

if __name__ == "__main__":
    try:
        html_content = pyperclip.paste()

        if not html_content:
            print("Clipboard is empty.")
        else:
            print(f"HTML content length: {len(html_content)} characters")

            # Save debug HTML
            save_debug_html(html_content)

            # Check for Reddit-specific elements
            if 'shreddit-post' in html_content:
                print("✓ Found shreddit-post elements")
            else:
                print("✗ No shreddit-post elements found")

            if 'shreddit-comment' in html_content:
                print("✓ Found shreddit-comment elements")
            else:
                print("✗ No shreddit-comment elements found")

            result = extract_reddit_post_and_messages(html_content)
            pyperclip.copy(result)
            print("\n" + "="*50)
            print(result)
            print("="*50)
            print("\nResults copied to clipboard.")

    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()
