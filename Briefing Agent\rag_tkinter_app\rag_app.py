import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import requests # For LLM interaction later
import re # For removing thinking tags
import datetime # To get the current date

class RAGApp:
    def __init__(self, root):
        self.root = root
        self.root.title("User Defined RAG LLM Node")
        self.root.geometry("1200x1050") # Made window wider and longer

        self.profiles_dir = "profiles"
        if not os.path.exists(self.profiles_dir):
            os.makedirs(self.profiles_dir)

        self.file_list = []
        self.current_profile_name = tk.StringVar()
        self.remove_thinking_var = tk.BooleanVar(value=False)
        self.show_payload_var = tk.BooleanVar(value=True) # Variable for showing payload

        # --- Main Paned Window ---
        main_paned_window = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        main_paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- Left Pane (Controls) ---
        left_frame_outer = ttk.Frame(main_paned_window, width=400, height=680)
        left_frame_outer.pack_propagate(False) # Prevent resizing based on content
        main_paned_window.add(left_frame_outer, weight=1)

        left_canvas = tk.Canvas(left_frame_outer)
        left_scrollbar = ttk.Scrollbar(left_frame_outer, orient="vertical", command=left_canvas.yview)
        left_scrollable_frame = ttk.Frame(left_canvas)

        left_scrollable_frame.bind(
            "<Configure>",
            lambda e: left_canvas.configure(
                scrollregion=left_canvas.bbox("all")
            )
        )

        left_canvas.create_window((0, 0), window=left_scrollable_frame, anchor="nw")
        left_canvas.configure(yscrollcommand=left_scrollbar.set)

        left_canvas.pack(side="left", fill="both", expand=True)
        left_scrollbar.pack(side="right", fill="y")


        # --- LLM Configuration ---
        llm_frame = ttk.LabelFrame(left_scrollable_frame, text="LLM Configuration")
        llm_frame.pack(padx=10, pady=10, fill="x")

        ttk.Label(llm_frame, text="LLM Address:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.llm_address_entry = ttk.Entry(llm_frame, width=30) # Adjusted width
        self.llm_address_entry.insert(0, "http://127.0.0.1:7856")
        self.llm_address_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        llm_buttons_frame = ttk.Frame(llm_frame)
        llm_buttons_frame.grid(row=0, column=2, padx=5, pady=0, sticky="e")
        ttk.Button(llm_buttons_frame, text="Test", command=self.test_llm_connection, width=5).pack(side=tk.LEFT, padx=(0,2))
        ttk.Button(llm_buttons_frame, text="Get Models", command=self.fetch_llm_models, width=10).pack(side=tk.LEFT)


        ttk.Label(llm_frame, text="LLM Model:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.llm_name_combobox = ttk.Combobox(llm_frame, width=38) # Adjusted width
        self.llm_name_combobox.insert(0, "default-model") # Default or previously saved
        self.llm_name_combobox.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        ttk.Label(llm_frame, text="API Key (Optional):").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.api_key_entry = ttk.Entry(llm_frame, width=30, show="*")
        self.api_key_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        llm_frame.columnconfigure(1, weight=1) # Allow entry/combobox to expand


        # --- File List Management ---
        files_frame = ttk.LabelFrame(left_scrollable_frame, text="Knowledge Files")
        files_frame.pack(padx=10, pady=10, fill="x")

        self.files_listbox = tk.Listbox(files_frame, selectmode=tk.SINGLE, height=6)
        self.files_listbox.pack(padx=5, pady=5, fill="x", expand=True)

        files_buttons_frame = ttk.Frame(files_frame)
        files_buttons_frame.pack(fill="x")
        ttk.Button(files_buttons_frame, text="Add File(s)", command=self.add_files).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(files_buttons_frame, text="Remove Selected", command=self.remove_selected_file).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(files_buttons_frame, text="Clear All Files", command=self.clear_all_files).pack(side=tk.LEFT, padx=5, pady=5)


        # --- Prompt Input ---
        prompt_frame = ttk.LabelFrame(left_scrollable_frame, text="Prompt")
        prompt_frame.pack(padx=10, pady=10, fill="both", expand=True)

        self.prompt_text = tk.Text(prompt_frame, height=8, wrap=tk.WORD)
        self.prompt_text.pack(padx=5, pady=5, fill="both", expand=True)


        # --- Profile Management ---
        profile_frame = ttk.LabelFrame(left_scrollable_frame, text="Profiles")
        profile_frame.pack(padx=10, pady=10, fill="x")

        ttk.Label(profile_frame, text="Profile Name:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.profile_name_entry = ttk.Entry(profile_frame, textvariable=self.current_profile_name, width=30)
        self.profile_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        profile_buttons_frame = ttk.Frame(profile_frame)
        profile_buttons_frame.grid(row=1, column=0, columnspan=2, pady=5)
        ttk.Button(profile_buttons_frame, text="Save Profile", command=self.save_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(profile_buttons_frame, text="Load Profile", command=self.load_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(profile_buttons_frame, text="Delete Profile", command=self.delete_profile).pack(side=tk.LEFT, padx=5)
        profile_frame.columnconfigure(1, weight=1)

        # --- Options Frame ---
        options_frame = ttk.Frame(left_scrollable_frame)
        options_frame.pack(padx=10, pady=(10,0), fill="x") # Added some top padding
        self.remove_thinking_checkbox = ttk.Checkbutton(
            options_frame,
            text="Remove <think>...</think> tags from LLM response",
            variable=self.remove_thinking_var
        )
        self.remove_thinking_checkbox.pack(side=tk.LEFT, pady=2, padx=5, anchor="w")

        self.show_payload_checkbox = ttk.Checkbutton(
            options_frame,
            text="Show payload in output",
            variable=self.show_payload_var
        )
        self.show_payload_checkbox.pack(side=tk.LEFT, pady=2, padx=5, anchor="w")


        # --- Process Button ---
        self.process_button = ttk.Button(left_scrollable_frame, text="Process with LLM", command=self.process_with_llm)
        self.process_button.pack(padx=10, pady=15, fill="x")


        # --- Right Pane (Output) ---
        right_frame = ttk.LabelFrame(main_paned_window, text="LLM Output", width=380, height=680)
        right_frame.pack_propagate(False)
        main_paned_window.add(right_frame, weight=1)

        self.output_text = tk.Text(right_frame, wrap=tk.WORD, state=tk.DISABLED)
        self.output_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

    def add_files(self):
        files = filedialog.askopenfilenames(title="Select files")
        if files:
            for file_path in files:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
                    self.files_listbox.insert(tk.END, os.path.basename(file_path) + f" ({os.path.dirname(file_path)})")
            self.update_listbox_tooltip()

    def remove_selected_file(self):
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select a file to remove.")
            return
        
        # Remove in reverse order to maintain correct indices
        for index in reversed(selected_indices):
            self.files_listbox.delete(index)
            del self.file_list[index]
        self.update_listbox_tooltip()

    def clear_all_files(self):
        if messagebox.askyesno("Confirm Clear", "Are you sure you want to remove all files from the list?"):
            self.file_list.clear()
            self.files_listbox.delete(0, tk.END)
            self.update_listbox_tooltip()

    def update_listbox_tooltip(self):
        # In a real app, you might use a custom tooltip widget for better UX
        # For simplicity, we'll just print to console or skip if it gets complex
        pass 

    def save_profile(self):
        profile_name = self.current_profile_name.get().strip()
        if not profile_name:
            messagebox.showerror("Error", "Please enter a profile name.")
            return

        profile_data = {
            "llm_name": self.llm_name_combobox.get(),
            "llm_address": self.llm_address_entry.get(),
            "api_key": self.api_key_entry.get(),
            "files": self.file_list,
            "prompt": self.prompt_text.get("1.0", tk.END).strip(),
            "remove_thinking": self.remove_thinking_var.get(),
            "show_payload": self.show_payload_var.get()
        }
        
        profile_path = os.path.join(self.profiles_dir, f"{profile_name}.json")
        try:
            with open(profile_path, "w") as f:
                json.dump(profile_data, f, indent=4)
            messagebox.showinfo("Success", f"Profile '{profile_name}' saved successfully.")
        except Exception as e:
            messagebox.showerror("Error Saving Profile", f"Could not save profile: {e}")

    def load_profile(self):
        profile_path = filedialog.askopenfilename(
            title="Load Profile",
            initialdir=self.profiles_dir,
            filetypes=(("JSON files", "*.json"), ("All files", "*.*"))
        )
        if not profile_path:
            return

        try:
            with open(profile_path, "r") as f:
                profile_data = json.load(f)

            self.llm_name_combobox.set(profile_data.get("llm_name", "default-model"))
            
            self.llm_address_entry.delete(0, tk.END)
            self.llm_address_entry.insert(0, profile_data.get("llm_address", "http://127.0.0.1:7856"))

            self.api_key_entry.delete(0, tk.END)
            self.api_key_entry.insert(0, profile_data.get("api_key", ""))

            self.remove_thinking_var.set(profile_data.get("remove_thinking", False))
            self.show_payload_var.set(profile_data.get("show_payload", True))

            self.file_list = profile_data.get("files", [])
            self.files_listbox.delete(0, tk.END)
            for file_path in self.file_list:
                if os.path.exists(file_path): # Check if file still exists
                    self.files_listbox.insert(tk.END, os.path.basename(file_path) + f" ({os.path.dirname(file_path)})")
                else:
                    self.files_listbox.insert(tk.END, f"[MISSING] {os.path.basename(file_path)}")


            self.prompt_text.delete("1.0", tk.END)
            self.prompt_text.insert("1.0", profile_data.get("prompt", ""))
            
            profile_name = os.path.splitext(os.path.basename(profile_path))[0]
            self.current_profile_name.set(profile_name)
            
            messagebox.showinfo("Success", f"Profile '{profile_name}' loaded.")
            self.update_listbox_tooltip()

        except Exception as e:
            messagebox.showerror("Error Loading Profile", f"Could not load profile: {e}")

    def delete_profile(self):
        profile_name = self.current_profile_name.get().strip()
        if not profile_name:
            # Try to get from a selection dialog if no name is entered
            profile_path_to_delete = filedialog.askopenfilename(
                title="Select Profile to Delete",
                initialdir=self.profiles_dir,
                filetypes=(("JSON files", "*.json"), ("All files", "*.*"))
            )
            if not profile_path_to_delete:
                messagebox.showwarning("Cancelled", "Profile deletion cancelled.")
                return
            profile_name = os.path.splitext(os.path.basename(profile_path_to_delete))[0]
        else:
            profile_path_to_delete = os.path.join(self.profiles_dir, f"{profile_name}.json")

        if not os.path.exists(profile_path_to_delete):
            messagebox.showerror("Error", f"Profile '{profile_name}' not found.")
            return

        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete profile '{profile_name}'? This cannot be undone."):
            try:
                os.remove(profile_path_to_delete)
                messagebox.showinfo("Success", f"Profile '{profile_name}' deleted.")
                if self.current_profile_name.get() == profile_name:
                    self.current_profile_name.set("") # Clear if current profile was deleted
            except Exception as e:
                messagebox.showerror("Error Deleting Profile", f"Could not delete profile: {e}")


    def process_with_llm(self):
        llm_name = self.llm_name_combobox.get().strip()
        llm_address = self.llm_address_entry.get().strip()
        api_key = self.api_key_entry.get().strip()
        prompt = self.prompt_text.get("1.0", tk.END).strip()

        if not llm_address:
            messagebox.showerror("Error", "LLM Address cannot be empty.")
            return
        if not llm_name: # Also check for model name
            messagebox.showerror("Error", "LLM Model cannot be empty. Try 'Fetch Models' or enter one.")
            return
        if not prompt:
            messagebox.showerror("Error", "Prompt cannot be empty.")
            return
        # Removed: if not self.file_list:
        # Removed:     messagebox.showwarning("Warning", "No files selected. Proceeding with prompt only.")

        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete("1.0", tk.END)
        self.output_text.insert(tk.END, "Processing...\n")
        self.output_text.see(tk.END) # Scroll to end
        self.root.update_idletasks() # Ensure UI updates

        context_content = ""
        if self.file_list: # Only show file processing messages if there are files
            self.output_text.insert(tk.END, "Reading files...\n")
            self.root.update_idletasks()
            for i, file_path in enumerate(self.file_list):
                self.output_text.insert(tk.END, f"Processing file {i+1}/{len(self.file_list)}: {os.path.basename(file_path)}\n")
                self.root.update_idletasks() # Update UI after each file message
                if not os.path.exists(file_path):
                    self.output_text.insert(tk.END, f"Warning: File not found - {file_path}\n")
                    continue
                try:
                    with open(file_path, "r", encoding='utf-8', errors='ignore') as f:
                        context_content += f.read() + "\n\n"
                except Exception as e:
                    self.output_text.insert(tk.END, f"Error reading file {file_path}: {e}\n")
            self.output_text.insert(tk.END, "File reading complete.\n")
            self.root.update_idletasks()
            
        full_prompt_for_llm = f"Context from files:\n{context_content}\n\nUser Prompt:\n{prompt}"
        
        # This is a very basic interaction. LM Studio API and other local LLMs might have different payload structures.
        # Typically, it's a JSON payload. For LM Studio, it's often OpenAI compatible.
        current_date_str = datetime.datetime.now().strftime("%A, %B %d, %Y")
        
        # Determine if it's a Gemini API call
        is_gemini_api = "generativelanguage.googleapis.com" in llm_address

        try:
            if is_gemini_api:
                if not api_key:
                    messagebox.showerror("Error", "API Key is required for Gemini API.")
                    self.output_text.insert(tk.END, "API Key is missing for Gemini.\n")
                    self.output_text.config(state=tk.DISABLED)
                    return

                # Construct Gemini API URL and payload
                # llm_address could be https://generativelanguage.googleapis.com
                # or https://generativelanguage.googleapis.com/v1beta/models
                # llm_name should be like: gemini-pro

                base_gemini_url_from_entry = llm_address
                # Ensure the base URL has the correct path segment for Gemini API
                if "generativelanguage.googleapis.com" in base_gemini_url_from_entry and \
                   "/v1beta/models" not in base_gemini_url_from_entry:
                    # If user provided just the domain, append the typical path
                    if base_gemini_url_from_entry.endswith("generativelanguage.googleapis.com"):
                        base_gemini_url_from_entry += "/v1beta/models"
                    elif base_gemini_url_from_entry.endswith("generativelanguage.googleapis.com/"):
                         base_gemini_url_from_entry += "v1beta/models"
                    # else, we assume user might be using a proxy or a different version path, so we don't alter it as much
                
                if not base_gemini_url_from_entry.endswith("/"):
                    base_gemini_url_from_entry += "/"
                
                api_url = f"{base_gemini_url_from_entry}{llm_name}:generateContent?key={api_key}"
                
                # For Gemini, combine system-like info with user prompt.
                gemini_prompt = (
                    f"You are a helpful assistant. Today is {current_date_str}.\n"
                    f"Please use the following context to answer the user's query:\n\n"
                    f"Context from files:\n{context_content}\n\n"
                    f"User Prompt:\n{prompt}"
                )
                payload = {
                    "contents": [{"parts": [{"text": gemini_prompt}]}],
                    # Optional: Add generationConfig if needed
                    # "generationConfig": {
                    #     "temperature": 0.7,
                    #     "maxOutputTokens": 1000,
                    # }
                }
                self.output_text.insert(tk.END, f"Sending request to Gemini API: {api_url}\n")
            else: # OpenAI-compatible / LM Studio
                system_message = f"You are a helpful assistant. Today is {current_date_str}. Use the provided context to answer the user's prompt."
                payload = {
                    "model": llm_name,
                    "messages": [
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": full_prompt_for_llm}
                    ],
                    "temperature": 0.7,
                    "stream": False
                }
                api_url = llm_address
                if not llm_address.endswith("/v1/chat/completions") and "chat/completions" not in llm_address:
                    if not llm_address.endswith("/"):
                        api_url += "/"
                    api_url += "v1/chat/completions"
                self.output_text.insert(tk.END, f"Sending request to: {api_url}\n")

            if self.show_payload_var.get():
                self.output_text.insert(tk.END, f"Payload (simplified): {json.dumps(payload, indent=2)[:500]}...\n")
            self.root.update_idletasks()

            response = requests.post(api_url, json=payload, timeout=180) # 180 seconds timeout
            response.raise_for_status()
            response_data = response.json()

            llm_response = ""
            if is_gemini_api:
                # Extract response from Gemini
                # Example: {"candidates":[{"content":{"parts":[{"text":"response"}]}}]}
                if response_data.get("candidates") and \
                   len(response_data["candidates"]) > 0 and \
                   response_data["candidates"][0].get("content") and \
                   response_data["candidates"][0]["content"].get("parts") and \
                   len(response_data["candidates"][0]["content"]["parts"]) > 0:
                    llm_response = response_data["candidates"][0]["content"]["parts"][0].get("text", "")
                else:
                    llm_response = f"Could not parse Gemini response: {json.dumps(response_data, indent=2)}"
            else: # OpenAI-compatible
                if response_data.get("choices") and len(response_data["choices"]) > 0:
                    message_obj = response_data["choices"][0].get("message", {})
                    llm_response = message_obj.get("content", "")
                    if not llm_response and response_data["choices"][0].get("text"): # Fallback
                        llm_response = response_data["choices"][0].get("text")
                elif response_data.get("text"): # Another fallback
                    llm_response = response_data.get("text")
                else:
                    llm_response = f"Could not parse OpenAI-compatible response: {json.dumps(response_data, indent=2)}"

            if self.remove_thinking_var.get():
                # Remove <think>...</think> and <thinking>...</thinking> tags, case-insensitive, multiline
                llm_response = re.sub(r"<think>.*?</think>", "", llm_response, flags=re.DOTALL | re.IGNORECASE)
                llm_response = re.sub(r"<thinking>.*?</thinking>", "", llm_response, flags=re.DOTALL | re.IGNORECASE)
                llm_response = llm_response.strip() # Clean up any leading/trailing whitespace after removal

            self.output_text.insert(tk.END, "\n--- LLM Response ---\n")
            self.output_text.insert(tk.END, llm_response)

        except requests.exceptions.RequestException as e:
            self.output_text.insert(tk.END, f"\n--- ERROR ---\nLLM API Request Failed: {e}\n")
            messagebox.showerror("LLM Error", f"Failed to connect or get response from LLM: {e}")
        except Exception as e:
            self.output_text.insert(tk.END, f"\n--- ERROR ---\nAn unexpected error occurred: {e}\n")
            messagebox.showerror("Processing Error", f"An unexpected error occurred: {e}")
        finally:
            self.output_text.config(state=tk.DISABLED)

    def test_llm_connection(self):
        llm_address = self.llm_address_entry.get().strip()
        if not llm_address:
            messagebox.showerror("Error", "LLM Address cannot be empty.")
            return

        # Try to hit the /v1/models endpoint as a basic test
        test_url = llm_address
        if not test_url.endswith("/"):
            test_url += "/"
        test_url += "v1/models" # A common endpoint for listing models

        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, f"\n--- Testing Connection to {llm_address} ---\n")
        self.output_text.insert(tk.END, f"Attempting GET request to: {test_url}\n")
        self.root.update_idletasks()

        try:
            response = requests.get(test_url, timeout=10) # 10 seconds timeout
            response.raise_for_status()
            self.output_text.insert(tk.END, f"Success! Status Code: {response.status_code}\n")
            if response.content:
                 try:
                    # Try to parse as JSON to see if it's a valid models list
                    models_data = response.json()
                    if isinstance(models_data.get("data"), list):
                        self.output_text.insert(tk.END, f"Received model data: {len(models_data['data'])} model(s) found in response.\n")
                    else:
                        self.output_text.insert(tk.END, f"Received response, but 'data' array not found in JSON.\n")
                 except json.JSONDecodeError:
                    self.output_text.insert(tk.END, f"Received response, but it's not valid JSON.\nContent (first 100 chars): {response.text[:100]}\n")
            else:
                self.output_text.insert(tk.END, "Received empty response, but connection was successful.\n")

            messagebox.showinfo("Connection Test", f"Successfully connected to {llm_address} (Status: {response.status_code}).")
        except requests.exceptions.Timeout:
            self.output_text.insert(tk.END, "Error: Connection timed out.\n")
            messagebox.showerror("Connection Test", "Connection timed out.")
        except requests.exceptions.ConnectionError as e:
            self.output_text.insert(tk.END, f"Error: Connection failed. {e}\n")
            messagebox.showerror("Connection Test", f"Connection failed: {e}")
        except requests.exceptions.HTTPError as e:
            self.output_text.insert(tk.END, f"Error: HTTP error. Status: {e.response.status_code}. Response: {e.response.text[:200]}\n")
            messagebox.showerror("Connection Test", f"HTTP error: {e.response.status_code}\n{e.response.text[:200]}")
        except requests.exceptions.RequestException as e:
            self.output_text.insert(tk.END, f"Error: An unexpected request error occurred: {e}\n")
            messagebox.showerror("Connection Test", f"Request error: {e}")
        finally:
            self.output_text.config(state=tk.DISABLED)
            self.output_text.see(tk.END)


    def fetch_llm_models(self):
        llm_address = self.llm_address_entry.get().strip()
        if not llm_address:
            messagebox.showerror("Error", "LLM Address cannot be empty to fetch models.")
            return

        api_url = llm_address
        if not api_url.endswith("/"):
            api_url += "/"
        api_url += "v1/models" # Standard OpenAI-compatible endpoint

        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, f"\n--- Fetching Models from {api_url} ---\n")
        self.root.update_idletasks()

        try:
            response = requests.get(api_url, timeout=15) # 15 seconds timeout
            response.raise_for_status()
            models_data = response.json()

            # Expected structure: {"data": [{"id": "model1_id", ...}, {"id": "model2_id", ...}]}
            if "data" in models_data and isinstance(models_data["data"], list):
                model_ids = [model.get("id") for model in models_data["data"] if model.get("id")]
                if model_ids:
                    self.llm_name_combobox['values'] = model_ids
                    self.llm_name_combobox.set(model_ids[0]) # Select the first model
                    self.output_text.insert(tk.END, f"Successfully fetched {len(model_ids)} models.\n")
                    messagebox.showinfo("Fetch Models", f"Found {len(model_ids)} models. Check the dropdown.")
                else:
                    self.output_text.insert(tk.END, "No model IDs found in the response data.\n")
                    messagebox.showwarning("Fetch Models", "No model IDs found in the response.")
            else:
                self.output_text.insert(tk.END, f"Unexpected response structure. 'data' array not found or not a list.\nResponse: {json.dumps(models_data, indent=2)[:300]}\n")
                messagebox.showerror("Fetch Models", "Could not parse model list from response. Check output log.")

        except requests.exceptions.Timeout:
            self.output_text.insert(tk.END, "Error: Request timed out while fetching models.\n")
            messagebox.showerror("Fetch Models", "Request timed out.")
        except requests.exceptions.ConnectionError as e:
            self.output_text.insert(tk.END, f"Error: Connection failed. {e}\n")
            messagebox.showerror("Fetch Models", f"Connection failed: {e}")
        except requests.exceptions.HTTPError as e:
            self.output_text.insert(tk.END, f"Error: HTTP error. Status: {e.response.status_code}. Response: {e.response.text[:200]}\n")
            messagebox.showerror("Fetch Models", f"HTTP error: {e.response.status_code}\n{e.response.text[:200]}")
        except json.JSONDecodeError:
            self.output_text.insert(tk.END, "Error: Failed to decode JSON response from server.\n")
            messagebox.showerror("Fetch Models", "Invalid JSON response from server.")
        except requests.exceptions.RequestException as e:
            self.output_text.insert(tk.END, f"Error: An unexpected request error occurred: {e}\n")
            messagebox.showerror("Fetch Models", f"Request error: {e}")
        finally:
            self.output_text.config(state=tk.DISABLED)
            self.output_text.see(tk.END)


if __name__ == "__main__":
    root = tk.Tk()
    app = RAGApp(root)
    root.mainloop()
