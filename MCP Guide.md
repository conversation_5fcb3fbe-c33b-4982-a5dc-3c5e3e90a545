---
title: MCP Guide
date: ************
areas:
  - InformationTechnology
description: important notes on MCP building
updated:
---

# Relationships
- guide for [[Model Context Protocol (MCP)]]
# Backlog
- Master and Document MCP Setup in Vscode - Cline
- Master and Document MCP Setup in Generaic MCP Client
- Learn "basic" MCP servers with Python Script

# Notes
- https://www.dailydoseofds.com/model-context-protocol-crash-course-part-3/
## Global Installation
```
// ask npm where it drops global executables
npm bin -g
```


```
// install the "start talking to MCP server" command
`npm install -g whatever-the-f-your-mcp-is-listed-as`
```

```
// invoke the command in whatever-the-f-your-mcp-client-is in the JSON setting
"command": "cmd.exe",
      "args": [
        "/c",
        "npx @upstash/context7-mcp@latest stdio"
      ],
      "transportType": "stdio"
```

# Log
- ************: [Massive Issues with Local Installing an MCP Server in Cline, switched to Global Install] Despite the servers being installable from button list, it wasn't working. I had to work with a global install (instead of local install like I think the extension was trying to do) of the MCP server (like context7) and straight up invoke the command. Works ok now.




