{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://clients2.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwOi8vZ3Z0MS5jb20A", false, 0], "server": "https://r3---sn-uhvcpax0n5-hjvd.gvt1.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACUAAABodHRwczovL2Nocm9tZXdlYnN0b3JlLmdvb2dsZWFwaXMuY29tAAAA", false, 0], "server": "https://chromewebstore.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "network_stats": {"srtt": 42248}, "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973250000866", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABoAAABodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbQAA", false, 0], "network_stats": {"srtt": 34854}, "server": "https://www.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973255619980", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 27035}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://w3-reporting-nel.reddit.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://preview.redd.it", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://ad-delivery.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973340836444", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://styles.redditmedia.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://a.thumbs.redditmedia.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://b.thumbs.redditmedia.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973342433188", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "server": "https://td.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973342439084", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://o418887.ingest.sentry.io", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973343267073", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.aws.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.gcp.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://www.redditstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "server": "https://www.reddit.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "network_stats": {"srtt": 33724}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "network_stats": {"srtt": 34857}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "network_stats": {"srtt": 44324}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "network_stats": {"srtt": 41845}, "server": "https://googleads.g.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "**************364", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "network_stats": {"srtt": 46305}, "server": "https://id.rlcdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973344484723", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "network_stats": {"srtt": 38017}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973344552025", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", false, 0], "network_stats": {"srtt": 26669}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973343226811", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "network_stats": {"srtt": 35214}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396973343904672", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3JlZGRpdC5jb20AAA==", true, 0], "network_stats": {"srtt": 33501}, "server": "https://www.gstatic.com", "supports_spdy": true}], "supports_quic": {"address": "2607:fb90:2522:d4c4:1111:a074:d5ae:730b", "used_quic": true}, "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}