#!/usr/bin/env python3
import os
import sys
import argparse
import whisper
import subprocess
import tempfile
import shutil
import logging
from pathlib import Path
from typing import List, Optional

# --- Configuration ---
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.wmv', '.flv']
AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac']
MEDIA_EXTENSIONS = set(VIDEO_EXTENSIONS + AUDIO_EXTENSIONS)
TRANSCRIPT_FORMAT = 'md'
CHUNK_SECONDS = 30
WHISPER_MODEL = "large-v3"
USE_FP16 = False

# --- Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def check_dependencies() -> bool:
    if shutil.which("ffmpeg") is None or shutil.which("ffprobe") is None:
        logging.error("ffmpeg/ffprobe not found in PATH.")
        return False
    return True

def format_time(seconds: float) -> str:
    integer = int(seconds)
    ms = int((seconds - integer) * 1000)
    h, rem = divmod(integer, 3600)
    m, s = divmod(rem, 60)
    return f"{h:02}:{m:02}:{s:02}"

def get_audio_stream_indices(media_path: Path) -> List[int]:
    cmd = [
        'ffprobe', '-v', 'error', '-select_streams', 'a',
        '-show_entries', 'stream=index', '-of', 'csv=p=0', str(media_path)
    ]
    try:
        out = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=True)
        return [int(line) for line in out.stdout.splitlines() if line.strip().isdigit()]
    except Exception as e:
        logging.warning(f"ffprobe error on {media_path.name}: {e}")
        return []

def extract_audio_track(media_path: Path, stream_idx: int, temp_dir: Path) -> Optional[Path]:
    stem = "".join(c if c.isalnum() else "_" for c in media_path.stem)
    out_wav = temp_dir / f"{stem}_track_{stream_idx}.wav"
    cmd = [
        'ffmpeg', '-y', '-i', str(media_path),
        '-map', f"0:{stream_idx}", '-ac', '1', '-ar', '16000',
        '-vn', '-acodec', 'pcm_s16le', str(out_wav)
    ]
    res = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    if res.returncode == 0 and out_wav.exists():
        return out_wav
    logging.error(f"FFmpeg failed for {media_path.name} track {stream_idx}: {res.stderr}")
    return None

def transcribe_file(media_path: Path, model):
    if media_path.suffix.lower() not in MEDIA_EXTENSIONS:
        logging.error(f"Unsupported file type: {media_path.suffix}")
        return

    streams = get_audio_stream_indices(media_path)
    if not streams:
        logging.warning("No audio streams found; skipping.")
        return

    with tempfile.TemporaryDirectory() as td:
        temp = Path(td)
        for idx in streams:
            wav = extract_audio_track(media_path, idx, temp)
            if not w
