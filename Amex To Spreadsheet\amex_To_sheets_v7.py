import tkinter as tk
from tkinter import filedialog
import os

def count_lines_in_file(filepath):
    """Count the number of lines in a text file."""
    with open(filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        return len(lines)

def select_file_and_count_lines():
    """Open a file dialog to select a text file and count its lines."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    file_path = filedialog.askopenfilename(
        title="Select a text file",
        filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
    )

    if file_path:
        line_count = count_lines_in_file(file_path)
        print(f"The file '{os.path.basename(file_path)}' has {line_count} lines.")
    else:
        print("No file was selected.")

if __name__ == "__main__":
    select_file_and_count_lines()
