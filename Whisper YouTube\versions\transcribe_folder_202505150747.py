import os
import whisper
import subprocess
import tempfile
import shutil
import logging
from tkinter import filedialog, Tk
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import List, Optional, Tuple

# --- Configuration ---
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.wmv', '.flv'] # Added more common extensions
TRANSCRIPT_FORMAT = 'txt'
CHUNK_SECONDS = 60  # Group transcript text into chunks of this many seconds
WHISPER_MODEL = "medium" # Consider "base", "small" for lower memory/faster processing
# Set to True if you have a compatible GPU and CUDA installed
USE_FP16 = False # Set to False if using CPU or experiencing issues

# --- Setup Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(), # Output logs to console
        # Optional: Uncomment to also log to a file
        # logging.FileHandler("transcription.log", mode='a', encoding='utf-8')
    ]
)

# --- Helper Functions ---

def check_dependencies():
    """Check if ffmpeg and ffprobe are accessible."""
    if shutil.which("ffmpeg") is None:
        logging.error("ffmpeg not found. Please install ffmpeg and ensure it's in your system's PATH.")
        return False
    if shutil.which("ffprobe") is None:
        logging.error("ffprobe not found. Please install ffmpeg (which includes ffprobe) and ensure it's in your system's PATH.")
        return False
    logging.info("ffmpeg and ffprobe found.")
    return True

def choose_folder() -> Optional[Path]:
    """Opens a dialog to choose a folder."""
    root = Tk()
    root.withdraw() # Hide the main tkinter window
    folder_path = filedialog.askdirectory(title="Select Folder Containing Video Files")
    root.destroy() # Destroy the root window after selection
    if folder_path:
        logging.info(f"Selected folder: {folder_path}")
        return Path(folder_path)
    else:
        logging.warning("No folder selected.")
        return None

def format_time(seconds: float) -> str:
    """Converts seconds to HH:MM:SS format."""
    try:
        td = timedelta(seconds=int(seconds))
        total_seconds = int(td.total_seconds())
        hours, remainder = divmod(total_seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        return f'{hours:02}:{minutes:02}:{secs:02}'
    except (ValueError, TypeError):
        logging.warning(f"Could not format invalid time value: {seconds}. Using 00:00:00.")
        return '00:00:00'

def get_audio_stream_indices(video_path: Path) -> List[int]:
    """Returns a list of audio stream indices using ffprobe."""
    command = [
        'ffprobe', '-v', 'error', '-select_streams', 'a',
        '-show_entries', 'stream=index', '-of', 'csv=p=0', str(video_path)
    ]
    try:
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, check=True, encoding='utf-8' # Added check=True for easier error catching
        )
        indices = [int(line.strip()) for line in result.stdout.strip().split('\n') if line.strip().isdigit()]
        if not indices:
             logging.warning(f"No audio streams found by ffprobe for: {video_path.name}")
        return indices
    except subprocess.CalledProcessError as e:
        logging.error(f"ffprobe failed for {video_path.name}. Error: {e.stderr.strip()}")
        return []
    except Exception as e:
        logging.error(f"An unexpected error occurred during ffprobe execution for {video_path.name}: {e}")
        return []

def extract_audio_track(video_path: Path, stream_index: int, temp_dir: Path) -> Optional[Path]:
    """Extracts a specific audio stream to a WAV file."""
    # Use a unique name within the temp dir based on video name and track index
    safe_video_name = "".join(c if c.isalnum() else "_" for c in video_path.stem)
    audio_filename = f"{safe_video_name}_track_{stream_index}.wav"
    audio_path = temp_dir / audio_filename

    command = [
        'ffmpeg', '-y', '-i', str(video_path),
        '-map', f'0:a:{stream_index}', # More specific stream mapping (0:a:index)
        '-ac', '1',         # Mono channel
        '-ar', '16000',      # 16kHz sample rate (optimal for Whisper)
        '-vn',              # No video output
        '-acodec', 'pcm_s16le', # Explicitly specify WAV codec
        str(audio_path)
    ]
    try:
        logging.info(f"Extracting audio track index {stream_index} from {video_path.name}...")
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, check=False, encoding='utf-8' # Don't check=True, check manually
        )
        if result.returncode != 0:
            logging.error(f"ffmpeg failed to extract track {stream_index} from {video_path.name}. Error: {result.stderr.strip()}")
            return None
        if not audio_path.exists() or audio_path.stat().st_size == 0:
            logging.warning(f"Extracted audio file for stream {stream_index} is missing or empty: {audio_path}")
            return None
        logging.info(f"Successfully extracted audio to: {audio_path.name}")
        return audio_path
    except FileNotFoundError:
         logging.error(f"ffmpeg command failed. Is ffmpeg installed and in PATH? Command: {' '.join(command)}")
         return None
    except Exception as e:
        logging.error(f"An unexpected error occurred during ffmpeg extraction for track {stream_index} of {video_path.name}: {e}")
        return None

# --- Main Processing Function ---

def transcribe_video(video_path: Path, model):
    """Transcribes audio tracks from a single video file."""
    logging.info(f"Processing: {video_path.name}")
    stream_indices = get_audio_stream_indices(video_path)

    if not stream_indices:
        logging.warning(f"No valid audio tracks found or ffprobe failed for {video_path.name}. Skipping.")
        return

    num_tracks = len(stream_indices)
    base_name = video_path.stem # Get filename without extension

    # Use a single temporary directory for all tracks of this video
    with tempfile.TemporaryDirectory(prefix=f"whisper_{base_name}_") as temp_dir_str:
        temp_dir = Path(temp_dir_str)
        logging.info(f"Created temporary directory: {temp_dir}")

        for stream_index in stream_indices:
            audio_path = extract_audio_track(video_path, stream_index, temp_dir)

            if not audio_path:
                logging.warning(f"Skipping transcription for track index {stream_index} of {video_path.name} due to extraction failure.")
                continue # Move to the next track

            try:
                logging.info(f"Transcribing audio track index {stream_index} from {audio_path.name}...")
                # Use fp16=True if GPU is available and compatible for faster inference
                result = model.transcribe(str(audio_path), fp16=USE_FP16)
                logging.info(f"Transcription complete for track {stream_index}.")

            except Exception as e:
                logging.error(f"Error during Whisper transcription for track {stream_index} of {video_path.name}: {e}")
                continue # Move to the next track

            # --- Process and Write Transcript ---
            track_chunks = {}
            segments = result.get('segments', [])

            if not segments:
                logging.warning(f"No segments found in transcription result for track {stream_index} of {video_path.name}.")
            else:
                for segment in segments:
                    start_time = segment.get('start')
                    text = segment.get('text', '').strip()
                    if start_time is not None and text:
                        try:
                           chunk_index = int(start_time) // CHUNK_SECONDS
                           track_chunks.setdefault(chunk_index, []).append(text)
                        except (ValueError, TypeError):
                           logging.warning(f"Invalid start time '{start_time}' encountered in segment for track {stream_index}. Skipping segment.")

            # Determine output filename
            if num_tracks > 1:
                output_filename = f"{base_name} (Track {stream_index}).{TRANSCRIPT_FORMAT}"
            else:
                output_filename = f"{base_name}.{TRANSCRIPT_FORMAT}"
            output_path = video_path.parent / output_filename # Place transcript in the same folder as the video

            logging.info(f"Writing transcript to: {output_path}")
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    if not track_chunks:
                         logging.info(f"Writing empty transcript file for track {stream_index} as no text was transcribed.")
                         # Optionally write a placeholder or leave empty
                         # f.write(f"{format_time(0)}\n[No speech detected or transcribed]\n")
                         f.write(f"{format_time(0)}\n") # Keep original behavior: timestamp for empty file
                    else:
                        max_chunk_index = max(track_chunks.keys())
                        # Ensure all chunks from 0 up to max_chunk_index are written, even if empty
                        for i in range(max_chunk_index + 1):
                            timestamp = format_time(i * CHUNK_SECONDS)
                            chunk_text = " ".join(track_chunks.get(i, [])) # Get text or empty list if chunk index missing
                            f.write(f"{timestamp} {chunk_text}\n\n") # Add space between timestamp and text
            except IOError as e:
                logging.error(f"Error writing transcript file {output_path}: {e}")
            except Exception as e:
                 logging.error(f"An unexpected error occurred while writing transcript for track {stream_index}: {e}")

        # Temporary directory is automatically cleaned up upon exiting the 'with' block
        logging.info(f"Finished processing tracks for {video_path.name}. Temporary directory {temp_dir} removed.")


# --- Main Execution ---

def main():
    """Main function to select folder and process videos."""
    if not check_dependencies():
        return # Stop if ffmpeg/ffprobe are missing

    folder_path = choose_folder()
    if not folder_path:
        return # Exit if no folder was selected

    try:
        logging.info(f"Loading Whisper model: {WHISPER_MODEL}...")
        model = whisper.load_model(WHISPER_MODEL)
        logging.info("Whisper model loaded successfully.")
    except Exception as e:
        logging.error(f"Failed to load Whisper model '{WHISPER_MODEL}'. Error: {e}")
        logging.error("Ensure you have enough memory/VRAM and the model name is correct.")
        return

    processed_files = 0
    skipped_files = 0
    failed_files = 0

    video_files_to_process = []
    for item in os.listdir(folder_path):
        item_path = folder_path / item
        if item_path.is_file() and item_path.suffix.lower() in VIDEO_EXTENSIONS:
            video_files_to_process.append(item_path)
        elif item_path.is_file():
            logging.debug(f"Skipping non-video file: {item_path.name}")
        else:
            logging.debug(f"Skipping non-file item (e.g., directory): {item_path.name}")

    if not video_files_to_process:
        logging.warning(f"No video files found with extensions {VIDEO_EXTENSIONS} in {folder_path}")
        return

    logging.info(f"Found {len(video_files_to_process)} video files to process.")

    for video_path in video_files_to_process:
        try:
            transcribe_video(video_path, model)
            processed_files += 1
        except Exception as e:
            # This is the crucial part: catch unexpected errors during a single video's processing
            logging.error(f"!!! Critical error processing {video_path.name}. Skipping this file. Error: {e}", exc_info=True) # Log traceback
            failed_files += 1
        finally:
            # Optional: Add garbage collection or VRAM clearing if memory issues persist between files
            # import gc; import torch; gc.collect(); torch.cuda.empty_cache()
            pass


    logging.info("="*20 + " Processing Summary " + "="*20)
    logging.info(f"Total video files found: {len(video_files_to_process)}")
    logging.info(f"Successfully processed (or attempted): {processed_files}")
    logging.info(f"Failed due to critical errors: {failed_files}")
    # Note: Skipped count isn't explicitly tracked here, but represents files where extraction/transcription failed internally but didn't crash the main loop.
    logging.info("Script finished.")


if __name__ == '__main__':
    main()