import yt_dlp
import os

def download_youtube_audio(url):
    """
    Downloads the highest quality audio from a YouTube URL to a specified directory.
    """
    # Output directory as specified by the user.
    # Using forward slashes as they work across OSes in Python strings and yt-dlp.
    output_directory = "C:/Users/<USER>/Videos"

    # yt-dlp typically creates the output directory if it doesn't exist.
    # For explicit directory creation, you could uncomment the following:
    # if not os.path.exists(output_directory):
    #     try:
    #         os.makedirs(output_directory)
    #         print(f"Created directory: {output_directory}")
    #     except OSError as e:
    #         print(f"Error creating directory {output_directory}: {e}")
    #         return

    ydl_opts = {
        'format': 'm4a/bestaudio/best',  # Request best audio, prefer m4a container.
                                         # Falls back to bestaudio if m4a not directly available.
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',  # Use FFmpeg to extract audio.
            'preferredcodec': 'm4a',      # Preferred audio codec.
            # 'preferredquality': '192',  # Optional: FFmpeg's quality setting. 
                                         # yt-dlp default for m4a is VBR quality '5'.
                                         # For highest quality, 'bestaudio' in format is key.
        }],
        'outtmpl': os.path.join(output_directory, '%(title)s.%(ext)s'), # Output path and filename template.
        'noplaylist': True,  # If URL is part of a playlist, download only the single video.
    }

    print(f"\nAttempting to download audio from: {url}")
    print(f"Audio will be saved to: {os.path.join(output_directory, '%(title)s.m4a')}") # Show example output
    # For debugging, you can print the full options:
    # print(f"yt-dlp options being used: {ydl_opts}")

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            ydl.download([url])
            print(f"\nAudio downloaded successfully and saved in '{output_directory}'.")
        except yt_dlp.utils.DownloadError as e:
            print(f"\nAn error occurred during download with yt-dlp: {e}")
        except Exception as e:
            print(f"\nAn unexpected error occurred: {e}")

if __name__ == "__main__":
    video_url = input("Please enter the YouTube video URL: ")
    if video_url.strip():
        download_youtube_audio(video_url.strip())
    else:
        print("No URL provided. Exiting.")
