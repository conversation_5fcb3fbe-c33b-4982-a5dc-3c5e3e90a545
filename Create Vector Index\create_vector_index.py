import os
import json
import hashlib
import time
import datetime
import frontmatter
import requests
import re
from tkinter import filedialog, Tk
import tiktoken # For token counting, assuming nomic-embed-text-v1.5 is similar to OpenAI models

# --- Configuration ---
EMBEDDING_API_URL = "http://127.0.0.1:7856/v1/embeddings"
EMBEDDING_MODEL_NAME = "nomic-ai/nomic-embed-text-v1.5" # Corrected model name
CLASS_NAME = "SmartSource"
OUTPUT_FILENAME = "vector_index.json"

# Initialize tiktoken encoder for nomic-embed-text-v1.5
# This might need adjustment if nomic has a specific tokenizer.
# For now, cl100k_base is a common one for newer embedding models.
try:
    tokenizer = tiktoken.encoding_for_model("text-embedding-ada-002") # Fallback, nomic might use a different one
except:
    tokenizer = tiktoken.get_encoding("cl100k_base")


def get_embedding_and_tokens(text_content):
    """
    Computes embedding vector and token count for the given text using LM Studio API.
    """
    headers = {"Content-Type": "application/json"}
    data = {
        "model": EMBEDDING_MODEL_NAME,
        "input": text_content
    }
    try:
        response = requests.post(EMBEDDING_API_URL, headers=headers, json=data)
        response.raise_for_status()  # Raise an exception for HTTP errors
        result = response.json()
        
        embedding = None
        api_tokens = None

        # The API response structure might vary. Common structures:
        if "data" in result and isinstance(result["data"], list) and len(result["data"]) > 0:
            if "embedding" in result["data"][0]:
                embedding = result["data"][0]["embedding"]
        
        if "usage" in result and "total_tokens" in result["usage"]:
            api_tokens = result["usage"]["total_tokens"]
        elif "usage" in result and "prompt_tokens" in result["usage"]: # some APIs use prompt_tokens
            api_tokens = result["usage"]["prompt_tokens"]

        if embedding is None:
            print(f"Warning: Could not extract embedding from API response: {result}")
            return None, 0

        # If API provides token count, use it. Otherwise, estimate with tiktoken.
        if api_tokens is not None:
            token_count = api_tokens
        else:
            token_count = len(tokenizer.encode(text_content))
            print(f"Warning: API did not provide token count. Estimated with tiktoken: {token_count}")
            
        return embedding, token_count
    except requests.exceptions.RequestException as e:
        print(f"Error calling embedding API: {e}")
        print(f"Response content: {response.content if 'response' in locals() else 'No response'}")
        return None, 0
    except json.JSONDecodeError:
        print(f"Error decoding JSON response from embedding API.")
        print(f"Response content: {response.content if 'response' in locals() else 'No response'}")
        return None, 0


def process_file(filepath):
    """
    Processes a single Markdown file to extract metadata, content, and generate embeddings.
    """
    print(f"Processing file: {filepath}...")
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content_with_frontmatter = f.read()
        
        # 1. Parse Frontmatter to get file content
        # We are not using frontmatter metadata directly in the final output per new requirements,
        # but still need to parse it to separate content.
        post = frontmatter.loads(content_with_frontmatter)
        file_text_content = post.content # This is the content without frontmatter
        
        lines = file_text_content.splitlines()
        processed_log_entries = [] # This will store all log entries from this file

        # Regex to find markdown headings (to correctly ignore "# Log" section if needed, though not strictly required now)
        heading_regex = re.compile(r"^(#+)\s+(.*)")
            
        for i, line_text in enumerate(lines):
            # Pattern: "- YYYYMMDDHHmm: ["
            log_entry_match = re.match(r"^\s*-\s*\d{12}:\s*\[", line_text)
            if log_entry_match:
                entry_text = line_text.strip()
                entry_embedding_vector, entry_token_count = get_embedding_and_tokens(entry_text)
                
                if entry_embedding_vector:
                    log_entry_record = {
                        "filepath": filepath, # Store full filepath
                        "line_number": i,
                        "text": entry_text,
                        "embedding": {
                            EMBEDDING_MODEL_NAME: {
                                "vec": entry_embedding_vector,
                                "tokens": entry_token_count
                            }
                        }
                        # Optionally, could add file's frontmatter metadata here if needed later
                        # "metadata": metadata 
                    }
                    processed_log_entries.append(log_entry_record)
                else:
                    print(f"Skipping log entry due to embedding error: {filepath} line {i}")

        if processed_log_entries:
            print(f"Successfully processed {len(processed_log_entries)} log entries from {filepath}")
            return processed_log_entries # Return a list of log entries for this file
        else:
            print(f"No log entries found or processed in {filepath}")
            return [] # Return an empty list if no log entries

    except Exception as e:
        print(f"Error processing file {filepath}: {e}")
        import traceback
        traceback.print_exc()
        return [] # Return an empty list on error


def main():
    """
    Main function to select folders, process files, and create the vector index.
    """
    root = Tk()
    root.withdraw()  # Hide the main Tkinter window

    print("Please select the folder containing your Markdown (.md) files.")
    input_folder = filedialog.askdirectory(title="Select Input Markdown Folder")
    if not input_folder:
        print("No input folder selected. Exiting.")
        return

    print(f"Input folder selected: {input_folder}")

    print(f"Please select the folder where the '{OUTPUT_FILENAME}' should be saved.")
    output_folder = filedialog.askdirectory(title="Select Output Folder for Vector Index")
    if not output_folder:
        print("No output folder selected. Exiting.")
        return
    
    print(f"Output folder selected: {output_folder}")

    all_log_entries_list = [] # Changed from dictionary to a list
    
    for filename in os.listdir(input_folder):
        if filename.endswith(".md"):
            filepath = os.path.join(input_folder, filename)
            log_entries_from_file = process_file(filepath) # process_file now returns a list
            if log_entries_from_file: # If the list is not empty
                all_log_entries_list.extend(log_entries_from_file) # Add entries to the main list
    
    if not all_log_entries_list:
        print("No log entries processed or found in any file. Exiting.")
        return

    output_path = os.path.join(output_folder, OUTPUT_FILENAME)
    try:
        with open(output_path, 'w', encoding='utf-8') as outfile:
            # Re-added indent=4 for normal list formatting / pretty-printing
            json.dump(all_log_entries_list, outfile, indent=4, default=json_serial) 
        print(f"Vector index successfully created at: {output_path}")
        print(f"Total log line entries processed: {len(all_log_entries_list)}") # Added count
    except IOError as e:
        print(f"Error writing vector index to {output_path}: {e}")
    except Exception as e:
        print(f"An unexpected error occurred while writing the JSON file: {e}")

def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime.date, datetime.datetime)):
        return obj.isoformat()
    raise TypeError ("Type %s not serializable" % type(obj))

if __name__ == "__main__":
    main()
