{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "announcement_notification_service_first_run_time": "*****************", "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1382, "left": 10, "maximized": false, "right": 1276, "top": 10, "work_area_bottom": 1392, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "26933d2b-6624-43ff-836c-ed7b98c93d9f", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "install_signature": {"expire_date": "2025-09-06", "ids": ["ghbmnnjooekpmoecnnnilnnbdlolhkhi", "lmjegmlicamnimmfhcmpkclmigmmcbeh"], "invalid_ids": [], "salt": "iFZCzHRvR7XKtxHRHB/H2PXrqQAi8FnMnN19nKyY3pE=", "signature": "XdlOnFvRnQyoHFPAGfFnYoDq/nXM4bBGyTcB1W+4f+tNqBjliDyBPqnjci5297b/Ma7TKIOIoxKf3gMhfrVhKc6AIYQ0XfV29g2ktBoWYQ0OGfz/Cse67qMT2w/09FiiE0Y9j+8lh5q/yUdIS/11sEggIPRdU1Wv2lbEIh1fLGL7WdLXnDP86IFW4kA7fE1seaCEtAFLVQCZ1QDGed3GkVkmFbiPJWrygpYTCJ5oS9Shl8KyLpz3QYCvsvzjJfjTfpSEcPAURbAw3hWd8mOvbwkZ8T9A2JMfeYXpDVrOmDHEcpTICDOAhTZjkwETWHHokmHxxANSC/fK0d8EfqmtIQ==", "signature_format_version": 2, "timestamp": "*****************"}, "last_chrome_version": "137.0.7151.104"}, "gaia_cookie": {"changed_time": **********.074401, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "8fcbca91-b00a-4c2e-9418-c493f593462f"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "language_model_counters": {"en": 1}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "bPXWv51gLEtN8PCjDacTpa4sA2Hy94krZLx0d31n68Mclg9zap0BtNrN6t2nLwQjXS/iCZAnvlBxHPetnDVEDw=="}, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "fledge_join_blocked": {}}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.reddit.com:443,*": {"last_modified": "*****************", "setting": {"https://www.reddit.com/": {"couldShowBannerEvents": 1.3394381344028744e+16, "next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]reddit.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://www.reddit.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://www.reddit.com:443,*": {"last_modified": "13394381339285446", "setting": {"lastEngagementTime": 1.3394381339285442e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.7151.104", "creation_time": "13394381243767654", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13394381339285442", "last_time_obsolete_http_credentials_removed": **********.813351, "last_time_password_store_metrics_reported": **********.814186, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "6C5BA1B7908A294E6535911BB3AF5EDCDCA45D7EE6E2E7F3CD034CF32174745F"}, "default_search_provider_data": {"template_url_data": "20A04E9E8D73D74B748FE24C0F746644F899916D9B7FC86A7F0461D7E09CFEE3"}, "enterprise_signin": {"policy_recovery_token": "E1A2EE239AFF3CF1A03BAA1FBB67F2372C97F8F7E505E1B8146C894557A57E50"}, "extensions": {"ui": {"developer_mode": "A0713AEE99842DC05BC178F0F1037909C3A778A5EFF118BE027D5C87B8CB42CF"}}, "google": {"services": {"account_id": "122A25CF818E8EFA0BC20D84E933859EBA8DB881A3ED9DCCB959D0471797DBD0", "last_signed_in_username": "1522E3B9254C005B117D1383870DB8BE655965B4F3530808911FAAF75FB598A3", "last_username": "998ED4720402FB6C1ACF1F43F98CE814D7F6B4187AC9E10B8EDFD32A2754288B"}}, "homepage": "C2FB94168C4B8E5859313683A5A5B3A2A6D7A148547F222D9155CD438F0BE603", "homepage_is_newtabpage": "A9FCFC3F8E470BA1EE93A2D7958DD2F8CF86BE5EE3EC1999309479F502194BBF", "media": {"cdm": {"origin_data": "BD27A75D46D16E56D6AF1559DA614F5B275ADC5CA9DDA57328E56587D7782B67"}, "storage_id_salt": "2CE255EEBBC2D6F1A34AEB9A5DDCA4DEB86E864B7B9589881D849150F5DF48FA"}, "module_blocklist_cache_md5_digest": "BD9F5657879BB8ADEF5CF6246B96245775C211A764A5DF5AED85F601D68459C3", "pinned_tabs": "30A72FB93B2B9A76F5044DCA5916586DC298CA71E367DF940B7F803A56565E62", "prefs": {"preference_reset_time": "048B1C4905D9F346CC95B4AD67799BA18EE651BBA303F69135A4DA2E771F80A1"}, "safebrowsing": {"incidents_sent": "D81B7F2FFDC9219920B30831CBA57BA84BBCB46241E913465D4F47F3C1A29A56"}, "search_provider_overrides": "6B68571D26E89D28F1364ECADE0A19CD3917ED16F5D2FFC9E7E3812B1CB1A93A", "session": {"restore_on_startup": "47A67216655A27FE51039CB560A05C59F32D5A4FDD6DBFC0AF66227BA4CA68DD", "startup_urls": "49596C3CCD32E13DC55B4A500075A6A7828319F080C82E4CB36F3B6F29860546"}}}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13394640444604069", "hash_real_time_ohttp_key": "1gAg2YwSUVLr/1C9QwLcQlIinnSQolg4pEv/mcLI7dMTwg0ABAABAAI=", "metrics_last_log_time": "13394381243", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ8JaNxObD5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEIyXjcTmw+UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13394246399000000", "uma_in_sql_start_time": "13394381243812786"}, "sessions": {"event_log": [{"crashed": false, "time": "13394381243811472", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394381367226813", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}}