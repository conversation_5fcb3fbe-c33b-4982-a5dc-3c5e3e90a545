import re
from datetime import datetime

def reformat_transaction(input_text):
    transactions = []

    # Find all transactions using a pattern that captures each section, handling commas in the amounts
    transaction_pattern = r"(\d{2}/\d{2}/\d{4})(.+?)([-+]?\$?\d{1,3}(?:,\d{3})*\.\d{2})"
    matches = re.finditer(transaction_pattern, input_text, re.DOTALL)

    for match in matches:
        date_str, description_str, amount_str = match.groups()

        # Reformat date
        date_obj = datetime.strptime(date_str, "%m/%d/%Y")
        formatted_month = date_obj.strftime("%b").upper()
        formatted_day = str(date_obj.day)

        # Clean up description and amount
        description_str = ' '.join(description_str.strip().split())
        amount_str = amount_str.replace('+', '').replace(',', '').strip()

        # Format output for this transaction
        output = f"{formatted_month}\t{formatted_day}\tST\t{description_str}\t\t\t{amount_str.replace('$', '')}"
        transactions.append(output)

    # Join all formatted transactions into a single output text with line breaks
    return '\n'.join(transactions)

def process_transactions(input_file_path, output_file_path):
    try:
        with open(input_file_path, 'r') as file:
            input_text = file.read()
        output_text = reformat_transaction(input_text)
        with open(output_file_path, 'w') as file:
            file.write(output_text + '\n')
        print(f"Output written successfully to {output_file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

# Assuming the script is run where INPUT.txt and OUTPUT.txt are located
process_transactions('INPUT.txt', 'OUTPUT.txt')
