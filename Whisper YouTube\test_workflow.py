#!/usr/bin/env python3
"""
Test script to verify the updated workflow
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def test_main_script_imports():
    """Test that the main script imports correctly without LLM dependencies."""
    try:
        # This should work even without LLM server running
        import youtube_transcript_manager as ytm
        
        # Test that LLM-related functions are removed
        assert not hasattr(ytm, 'test_llm_connection'), "LLM functions should be removed from main script"
        assert not hasattr(ytm, 'generate_title_with_llm'), "LLM functions should be removed from main script"
        
        # Test that core functions exist
        assert hasattr(ytm, 'extract_video_metadata'), "Core functions should exist"
        assert hasattr(ytm, 'download_youtube_audio'), "Core functions should exist"
        assert hasattr(ytm, 'transcribe_audio_file'), "Core functions should exist"
        
        logging.info("✓ Main script imports correctly and LLM functions removed")
        return True
        
    except Exception as e:
        logging.error(f"✗ Main script import failed: {e}")
        return False

def test_title_generator_imports():
    """Test that the title generator script imports correctly."""
    try:
        import generate_chunk_titles as gct
        
        # Test that LLM functions exist in title generator
        assert hasattr(gct, 'test_llm_connection'), "LLM functions should exist in title generator"
        assert hasattr(gct, 'generate_title_with_llm'), "LLM functions should exist in title generator"
        
        # Test that hardcoded folder path is available
        assert hasattr(gct, 'HARDCODED_FOLDER_PATH'), "Hardcoded folder path should be available"
        
        logging.info("✓ Title generator script imports correctly")
        return True
        
    except Exception as e:
        logging.error(f"✗ Title generator import failed: {e}")
        return False

def create_test_transcript():
    """Create a test transcript file to verify title generation logic."""
    test_content = """---
date: 202405121430
title: Test Video
author: Test Author
url: https://youtu.be/test
---

- [00:00:00](https://youtu.be/test&t=0) - [Untitled Segment] - This is the first segment that should get a new title.

- [00:00:30](https://youtu.be/test&t=30) - [Existing Title] - This segment already has a title and should be preserved.

- [00:01:00](https://youtu.be/test&t=60) - [Untitled Segment] - This is another segment that should get a new title.

"""
    
    test_file = Path("test_transcript.md")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    logging.info(f"✓ Created test transcript: {test_file}")
    return test_file

def test_title_processing_logic():
    """Test the title processing logic without actually calling LLM."""
    try:
        import generate_chunk_titles as gct
        import re
        
        # Create test transcript
        test_file = create_test_transcript()
        
        # Read the file
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Use the same regex pattern as the script
        segment_pattern = r'- \[(\d{2}:\d{2}:\d{2})\]\([^)]*\) - \[(.+?)\] - (.+?)(?=\n- \[|\n\n|\Z)'
        segments = re.findall(segment_pattern, content, re.DOTALL)
        
        logging.info(f"Found {len(segments)} segments in test file")
        
        untitled_count = 0
        titled_count = 0
        
        for timestamp, current_title, text in segments:
            current_title = re.sub(r'\s+', ' ', current_title.strip())
            
            if current_title == "Untitled Segment":
                untitled_count += 1
                logging.info(f"  - {timestamp}: [Untitled Segment] - would be processed")
            else:
                titled_count += 1
                logging.info(f"  - {timestamp}: [{current_title}] - would be skipped")
        
        # Clean up
        test_file.unlink()
        
        if untitled_count == 2 and titled_count == 1:
            logging.info("✓ Title processing logic works correctly")
            return True
        else:
            logging.error(f"✗ Expected 2 untitled and 1 titled, got {untitled_count} untitled and {titled_count} titled")
            return False
            
    except Exception as e:
        logging.error(f"✗ Title processing test failed: {e}")
        return False

def main():
    """Run all tests."""
    logging.info("=== Testing Updated Workflow ===\n")
    
    tests = [
        ("Main Script Imports", test_main_script_imports),
        ("Title Generator Imports", test_title_generator_imports),
        ("Title Processing Logic", test_title_processing_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logging.info(f"Running: {test_name}")
        if test_func():
            passed += 1
        logging.info("")
    
    logging.info("=== Test Results ===")
    logging.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logging.info("✓ All tests passed! The workflow separation is working correctly.")
    else:
        logging.error("✗ Some tests failed. Please check the implementation.")

if __name__ == '__main__':
    main()
