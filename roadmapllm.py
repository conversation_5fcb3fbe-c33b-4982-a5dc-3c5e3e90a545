#!/usr/bin/env python3
"""
update_roadmaps.py - Automatically update roadmap sections in markdown files

This script scans markdown files in a folder for:
1. Files with populated "# Backlog" sections
2. Files with log entries dated after the 'updated' date in YAML frontmatter
3. Creates or updates "# Roadmap" sections with AI-generated content

Requirements:
- pip install pyyaml python-dateutil requests
"""

import os
import re
import yaml
from datetime import datetime, timedelta
from dateutil import parser as date_parser
from pathlib import Path
import random
import tkinter as tk
from tkinter import filedialog
import argparse
import requests
import json

# LM Studio configuration
LM_STUDIO_URL = "http://127.0.0.1:7856/v1/chat/completions"
MODEL_NAME = "qwen_qwq-32b"

# Hardcoded random files for context (you can modify these)
RANDOM_BACKLINKED_FILE = "project_notes.md"  # Modify this to an actual file in your system
RANDOM_LINKED_FILE = "resources.md"  # Modify this to an actual file in your system

def browse_folder():
    """Open a folder browser dialog and return the selected path."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    folder_path = filedialog.askdirectory(title="Select folder to scan for markdown files")
    root.destroy()
    return folder_path

def extract_yaml_frontmatter(content):
    """Extract YAML frontmatter from markdown content."""
    if not content.startswith('---'):
        return None, content
    
    try:
        # Find the end of frontmatter
        end_match = re.search(r'\n---\n', content)
        if not end_match:
            return None, content
        
        yaml_content = content[3:end_match.start()]
        remaining_content = content[end_match.end():]
        
        frontmatter = yaml.safe_load(yaml_content)
        return frontmatter, remaining_content
    except yaml.YAMLError:
        return None, content

def extract_section_content(content, section_name):
    """Extract content under a specific markdown section."""
    pattern = rf'^# {re.escape(section_name)}\s*$'
    match = re.search(pattern, content, re.MULTILINE)
    
    if not match:
        return ""
    
    start_pos = match.end()
    
    # Find the next section (starting with #)
    next_section = re.search(r'\n# ', content[start_pos:])
    if next_section:
        end_pos = start_pos + next_section.start()
        section_content = content[start_pos:end_pos]
    else:
        section_content = content[start_pos:]
    
    return section_content.strip()

def has_populated_backlog(content):
    """Check if the file has a populated backlog section."""
    backlog_content = extract_section_content(content, "Backlog")
    print(f"    Backlog content: '{backlog_content[:100]}...' (length: {len(backlog_content)})")

    if not backlog_content:
        print(f"    No backlog section found")
        return False

    # Remove empty lines and check if there's actual content
    lines = [line.strip() for line in backlog_content.split('\n') if line.strip()]
    print(f"    Backlog has {len(lines)} non-empty lines")
    return len(lines) > 0

def extract_log_dates(content):
    """Extract all dates from log entries in the content."""
    dates = []

    # Look for YYYYMMDDHHMM format (12 digits) - most specific first
    pattern_12 = r'\b(\d{12})\b'
    matches_12 = re.findall(pattern_12, content)
    for match in matches_12:
        try:
            # Parse as YYYYMMDDHHMM
            year = int(match[:4])
            month = int(match[4:6])
            day = int(match[6:8])
            hour = int(match[8:10])
            minute = int(match[10:12])
            parsed_date = datetime(year, month, day, hour, minute)
            dates.append(parsed_date)
            print(f"    Found 12-digit date: {match} -> {parsed_date}")
        except Exception as e:
            print(f"    Failed to parse 12-digit date '{match}': {e}")

    # Look for YYYYMMDD format (8 digits)
    pattern_8 = r'\b(\d{8})\b'
    matches_8 = re.findall(pattern_8, content)
    for match in matches_8:
        try:
            parsed_date = datetime.strptime(match, '%Y%m%d')
            dates.append(parsed_date)
            print(f"    Found 8-digit date: {match} -> {parsed_date}")
        except Exception as e:
            print(f"    Failed to parse 8-digit date '{match}': {e}")

    # Look for other common date patterns
    other_patterns = [
        r'\b\d{4}-\d{2}-\d{2}\b',  # YYYY-MM-DD
        r'\b\d{2}/\d{2}/\d{4}\b',  # MM/DD/YYYY
        r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',  # M/D/YY or MM/DD/YYYY
    ]

    for pattern in other_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            try:
                parsed_date = date_parser.parse(match, fuzzy=True)
                dates.append(parsed_date)
                print(f"    Found other date: {match} -> {parsed_date}")
            except Exception as e:
                print(f"    Failed to parse other date '{match}': {e}")

    return dates

def has_recent_log_entries(content, updated_date):
    """Check if there are log entries after the updated date."""
    log_dates = extract_log_dates(content)

    print(f"    Found log dates: {[d.strftime('%Y%m%d%H%M') for d in log_dates]}")

    if not log_dates:
        print(f"    No log dates found")
        return False

    # If no updated date, consider any log entry as recent
    if not updated_date:
        print(f"    No updated date, any log entry counts as recent")
        return len(log_dates) > 0

    print(f"    Comparing against updated date: {updated_date.strftime('%Y%m%d%H%M')}")

    # Check if any log date is after the updated date
    for log_date in log_dates:
        if log_date > updated_date:
            print(f"    Found recent log entry: {log_date.strftime('%Y%m%d%H%M')} > {updated_date.strftime('%Y%m%d%H%M')}")
            return True

    print(f"    No log entries after updated date")
    return False

def read_context_file(file_path, base_folder):
    """Read a context file if it exists."""
    full_path = os.path.join(base_folder, file_path)
    if os.path.exists(full_path):
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return ""
    return ""

def generate_roadmap_prompt(content, backlinked_content, linked_content):
    """Generate the prompt for AI roadmap creation."""
    prompt = f"""Based on your current context, (conversations, plans, backlog, logs). Determine what is the goal of the user in the current scope. Then make a best guess estimate of what is the "state" of the user. Then construct 3 past tasks you think the user has done and create 3 future tasks that you think the user should pursue. For each future task, include an indented bullet point with a critical sub-task that would take around 3 hours to complete (1 hour planning, 1 hour implement, 1 hour debrief). Pay special attention to any tools, training, and frameworks that are used or should be mentioned such ideas with single brackets. Just give a single bullet list giving the estimated goal, state, past tasks and future tasks. Avoid using headings.

Current File Content:
{content}

Backlinked File Content:
{backlinked_content}

Linked File Content:
{linked_content}"""

    return prompt

def call_lm_studio_api(prompt):
    """Call LM Studio API to generate roadmap content."""
    try:
        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": False
        }

        print(f"  Making API request to {LM_STUDIO_URL}...")
        print(f"  Timeout set to 180 seconds (3 minutes)...")

        response = requests.post(LM_STUDIO_URL, headers=headers, json=data, timeout=180)
        response.raise_for_status()

        result = response.json()
        if 'choices' in result and len(result['choices']) > 0:
            content = result['choices'][0]['message']['content']
            print(f"  ✓ Received response ({len(content)} characters)")
            return content
        else:
            print(f"  ✗ No choices in API response")
            return "Error: No response from AI model"

    except requests.exceptions.Timeout as e:
        print(f"  ✗ Request timed out after 180 seconds")
        print(f"  Note: Check your LM Studio server logs - the response might have been generated")
        print(f"  You can manually copy the response content from the server logs if needed")
        return f"Error: Request timed out - {str(e)}"
    except requests.exceptions.RequestException as e:
        print(f"  ✗ Network error: {e}")
        return f"Error: Failed to connect to LM Studio - {str(e)}"
    except json.JSONDecodeError as e:
        print(f"  ✗ JSON parsing error: {e}")
        print(f"  Raw response: {response.text[:500]}...")
        return "Error: Invalid response from AI model"
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")
        return f"Error: {str(e)}"

def extract_non_thinking_content(ai_response):
    """Extract content outside of <thinking> and <think> tags."""
    # Remove thinking tags and their content (both <thinking> and <think> variants)
    cleaned = re.sub(r'<thinking>.*?</thinking>', '', ai_response, flags=re.DOTALL)
    cleaned = re.sub(r'<think>.*?</think>', '', cleaned, flags=re.DOTALL)
    return cleaned.strip()

def create_manual_roadmap_from_server_log():
    """Create a roadmap from the server log content you provided."""
    # Updated content with 3 past tasks, 3 future tasks with 3-hour critical sub-tasks
    server_response = """<think>

Based on the MCP Guide content, the user is working on mastering Model Context Protocol setup across different environments. They've resolved some installation issues and are building comprehensive documentation.

</think>

• **Goal**: Create a comprehensive guide for Model Context Protocol (MCP) setup across multiple environments including [VSCode], generic clients, and [Python] implementations
• **Current State**: Mid-setup phase with basic configurations working, resolved local vs global installation issues, actively troubleshooting and documenting setup processes
• **Past Tasks Completed**:
  - Resolved local vs global server installation conflicts using [npx @upstash/context7-mcp@latest stdio]
  - Configured [VSCode] settings with JSON snippets for MCP client integration
  - Researched MCP fundamentals through online resources and crash course materials
• **Future Tasks to Pursue**:
  - Document the resolved local vs global install issue with step-by-step troubleshooting guide
    - Create comprehensive installation troubleshooting flowchart with common error scenarios and solutions
  - Create detailed [VSCode] setup documentation including required extensions and configuration files
    - Build complete [VSCode] MCP extension testing environment with sample configurations and validation scripts
  - Develop a basic [Python] script for MCP server using frameworks like [FastAPI] or [Flask]
    - Implement functional MCP server prototype with authentication, basic endpoints, and client connection testing"""

    return extract_non_thinking_content(server_response)

def update_roadmap_section(content, roadmap_content):
    """Add or update the RoadmapLLM section in the markdown content."""
    # Generate current timestamp for the header
    current_timestamp = datetime.now().strftime('%Y%m%d%H%M')
    roadmap_header = f"# RoadmapLLM {current_timestamp}"

    # Check if RoadmapLLM section already exists (with or without timestamp)
    roadmap_match = re.search(r'^# RoadmapLLM(?:\s+\d{12})?\s*$', content, re.MULTILINE)

    if roadmap_match:
        # Find the next section after RoadmapLLM
        start_pos = roadmap_match.start()
        next_section = re.search(r'\n# ', content[roadmap_match.end():])

        if next_section:
            # Replace entire RoadmapLLM section including header
            insert_pos = roadmap_match.end() + next_section.start()
            updated_content = (content[:start_pos] +
                             f"{roadmap_header}\n\n{roadmap_content}\n" +
                             content[insert_pos:])
        else:
            # Replace from RoadmapLLM to end
            updated_content = content[:start_pos] + f"{roadmap_header}\n\n{roadmap_content}\n"
    else:
        # Find the end of the Backlog section to insert RoadmapLLM after it
        backlog_match = re.search(r'^# Backlog\s*$', content, re.MULTILINE)

        if backlog_match:
            # Find the next section after Backlog
            backlog_start = backlog_match.end()
            next_section_after_backlog = re.search(r'\n# ', content[backlog_start:])

            if next_section_after_backlog:
                # Insert RoadmapLLM between Backlog and next section
                insert_pos = backlog_start + next_section_after_backlog.start()
                updated_content = (content[:insert_pos] +
                                 f"\n\n{roadmap_header}\n\n{roadmap_content}\n" +
                                 content[insert_pos:])
            else:
                # Insert RoadmapLLM after Backlog at the end
                updated_content = content + f"\n\n{roadmap_header}\n\n{roadmap_content}\n"
        else:
            # No Backlog section found, add RoadmapLLM at the end
            updated_content = content + f"\n\n{roadmap_header}\n\n{roadmap_content}\n"

    return updated_content

# This function is no longer needed since we're using timestamps in section headers
# Keeping it for backward compatibility but it just returns content unchanged
def update_yaml_frontmatter_timestamp(content):
    """No longer updates YAML frontmatter - timestamps are now in section headers."""
    return content

def process_markdown_file(file_path, base_folder):
    """Process a single markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract frontmatter (not used for timestamp anymore, but keep for potential other uses)
        _, main_content = extract_yaml_frontmatter(content)

        # Get updated date from RoadmapLLM section header
        updated_date = None
        roadmap_header_match = re.search(r'^# RoadmapLLM (\d{12})\s*$', main_content, re.MULTILINE)
        if roadmap_header_match:
            try:
                timestamp_str = roadmap_header_match.group(1)
                year = int(timestamp_str[:4])
                month = int(timestamp_str[4:6])
                day = int(timestamp_str[6:8])
                hour = int(timestamp_str[8:10])
                minute = int(timestamp_str[10:12])
                updated_date = datetime(year, month, day, hour, minute)
                print(f"  Found RoadmapLLM timestamp: {updated_date}")
            except Exception as e:
                print(f"  Error parsing RoadmapLLM timestamp: {e}")
        else:
            print(f"  No RoadmapLLM timestamp found (section doesn't exist or has no timestamp)")

        # Check conditions with debug info
        backlog_populated = has_populated_backlog(main_content)
        print(f"  Has populated backlog: {backlog_populated}")

        if not backlog_populated:
            return False, "No populated backlog section"

        recent_logs = has_recent_log_entries(main_content, updated_date)
        print(f"  Has recent log entries: {recent_logs}")

        if not recent_logs:
            return False, "No recent log entries"
        
        # Read context files
        backlinked_content = read_context_file(RANDOM_BACKLINKED_FILE, base_folder)
        linked_content = read_context_file(RANDOM_LINKED_FILE, base_folder)
        
        # Generate roadmap
        prompt = generate_roadmap_prompt(content, backlinked_content, linked_content)
        print(f"  Calling LM Studio API...")
        ai_response = call_lm_studio_api(prompt)

        # Check if API call failed due to timeout and offer fallback
        if ai_response.startswith("Error: Request timed out"):
            print(f"  API timed out. Using manual roadmap from server logs as fallback...")
            ai_response = create_manual_roadmap_from_server_log()

        roadmap_content = extract_non_thinking_content(ai_response)
        
        # Update the file with roadmap content
        updated_content = update_roadmap_section(content, roadmap_content)

        # Update the 'updated' timestamp in frontmatter
        final_content = update_yaml_frontmatter_timestamp(updated_content)

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(final_content)

        return True, "Successfully updated RoadmapLLM section with timestamp in header"
        
    except Exception as e:
        return False, f"Error processing file: {str(e)}"

def test_single_file(file_path):
    """Test a single file to see if it qualifies."""
    print(f"Testing file: {file_path}")

    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"File length: {len(content)} characters")

        # Extract frontmatter
        frontmatter, main_content = extract_yaml_frontmatter(content)
        print(f"Frontmatter: {frontmatter}")

        # Get updated date from RoadmapLLM section header
        updated_date = None
        roadmap_header_match = re.search(r'^# RoadmapLLM (\d{12})\s*$', main_content, re.MULTILINE)
        if roadmap_header_match:
            try:
                timestamp_str = roadmap_header_match.group(1)
                year = int(timestamp_str[:4])
                month = int(timestamp_str[4:6])
                day = int(timestamp_str[6:8])
                hour = int(timestamp_str[8:10])
                minute = int(timestamp_str[10:12])
                updated_date = datetime(year, month, day, hour, minute)
                print(f"Found RoadmapLLM timestamp: {updated_date}")
            except Exception as e:
                print(f"Error parsing RoadmapLLM timestamp: {e}")
        else:
            print("No RoadmapLLM timestamp found (section doesn't exist or has no timestamp)")

        # Check conditions with debug info
        backlog_populated = has_populated_backlog(main_content)
        print(f"Has populated backlog: {backlog_populated}")

        recent_logs = has_recent_log_entries(main_content, updated_date)
        print(f"Has recent log entries: {recent_logs}")

        qualifies = backlog_populated and recent_logs
        print(f"\nFinal result: File {'QUALIFIES' if qualifies else 'DOES NOT QUALIFY'} for roadmap generation")

    except Exception as e:
        print(f"Error testing file: {e}")

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description='Update roadmap sections in markdown files')
    parser.add_argument('--folder', '-f', help='Folder path to scan (if not provided, will open browser)')
    parser.add_argument('--test', '-t', help='Test a single file to see if it qualifies')
    args = parser.parse_args()

    # Test mode
    if args.test:
        test_single_file(args.test)
        return
    
    # Get folder path
    if args.folder:
        folder_path = args.folder
    else:
        folder_path = browse_folder()
    
    if not folder_path:
        print("No folder selected. Exiting.")
        return
    
    if not os.path.exists(folder_path):
        print(f"Folder does not exist: {folder_path}")
        return
    
    print(f"Scanning folder: {folder_path}")
    print(f"Using context files: {RANDOM_BACKLINKED_FILE}, {RANDOM_LINKED_FILE}")
    
    # Find all .md files in the top level
    md_files = [f for f in os.listdir(folder_path) 
                if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]
    
    if not md_files:
        print("No markdown files found in the folder.")
        return
    
    print(f"Found {len(md_files)} markdown files")
    
    processed_count = 0
    updated_count = 0
    
    for md_file in md_files:
        file_path = os.path.join(folder_path, md_file)
        print(f"\nProcessing: {md_file}")
        
        success, message = process_markdown_file(file_path, folder_path)
        processed_count += 1
        
        if success:
            updated_count += 1
            print(f"  ✓ {message}")
        else:
            print(f"  - {message}")
    
    print(f"\nSummary:")
    print(f"  Files processed: {processed_count}")
    print(f"  Files updated: {updated_count}")
    print(f"  Files skipped: {processed_count - updated_count}")

if __name__ == "__main__":
    main()
