import os
import shutil
import tkinter as tk
from tkinter import filedialog, messagebox
from pathlib import Path
import re

# Try to import PyYAML
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

def browse_for_folder(title):
    """Show folder selection dialog."""
    root = tk.Tk()
    root.withdraw()
    
    folder_path = filedialog.askdirectory(title=title)
    root.destroy()
    
    return folder_path

def check_yaml_value_basic(file_path, search_key, search_value):
    """
    Basic YAML front matter value checking without PyYAML.
    Only handles simple key: value pairs.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Check if file has YAML front matter
        if not content.startswith('---'):
            return False, "No YAML front matter found"
        
        # Find the end of front matter
        front_matter_end = content.find('---', 3)
        if front_matter_end == -1:
            return False, "Invalid YAML front matter (no closing ---)"
        
        front_matter_text = content[3:front_matter_end]
        
        # Simple text-based search for key: value pairs
        pattern = rf'^{re.escape(search_key)}:\s*{re.escape(search_value)}\s*$'
        
        if re.search(pattern, front_matter_text, re.MULTILINE):
            return True, "Match found (basic mode)"
        else:
            # Check if key exists with different value
            key_pattern = rf'^{re.escape(search_key)}:'
            if re.search(key_pattern, front_matter_text, re.MULTILINE):
                return False, f"Key found but value doesn't match '{search_value}'"
            else:
                return False, f"Key '{search_key}' not found"
    
    except Exception as e:
        return False, f"Error reading file: {e}"

def check_yaml_value_advanced(file_path, search_key, search_value):
    """
    Advanced YAML front matter value checking using PyYAML.
    Supports complex data types including lists.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Check if file has YAML front matter
        if not content.startswith('---'):
            return False, "No YAML front matter found"
        
        # Find the end of front matter
        front_matter_end = content.find('---', 3)
        if front_matter_end == -1:
            return False, "Invalid YAML front matter (no closing ---)"
        
        # Extract and parse YAML
        front_matter_text = content[3:front_matter_end].strip()
        try:
            front_matter_dict = yaml.safe_load(front_matter_text)
        except yaml.YAMLError as e:
            return False, f"YAML parsing error: {e}"
        
        if front_matter_dict is None:
            front_matter_dict = {}
        
        # Check if the key exists
        if search_key not in front_matter_dict:
            return False, f"Key '{search_key}' not found"
        
        # Get the current value
        current_value = front_matter_dict[search_key]
        
        # Handle different value types
        if isinstance(current_value, str):
            # Simple string comparison
            if current_value == search_value:
                return True, "Match found (advanced mode)"
        elif isinstance(current_value, list):
            # Check if search_value is in the list
            if search_value in [str(item) for item in current_value]:
                return True, "Match found in list (advanced mode)"
        else:
            # Handle other types by converting to string for comparison
            if str(current_value) == search_value:
                return True, "Match found (advanced mode)"
        
        return False, f"Key found but value doesn't match '{search_value}'"
    
    except Exception as e:
        return False, f"Error processing file: {e}"

def check_yaml_value(file_path, search_key, search_value):
    """
    Check if a YAML front matter key has a specific value.
    Returns (is_match, message)
    """
    if YAML_AVAILABLE:
        return check_yaml_value_advanced(file_path, search_key, search_value)
    else:
        return check_yaml_value_basic(file_path, search_key, search_value)

def move_file_safely(source_path, destination_folder):
    """
    Move a file to destination folder, handling name conflicts.
    Returns (success, new_path, message)
    """
    try:
        destination_folder = Path(destination_folder)
        source_path = Path(source_path)
        
        # Create destination folder if it doesn't exist
        destination_folder.mkdir(parents=True, exist_ok=True)
        
        # Determine the destination file path
        destination_path = destination_folder / source_path.name
        
        # Handle name conflicts
        counter = 1
        original_stem = source_path.stem
        original_suffix = source_path.suffix
        
        while destination_path.exists():
            new_name = f"{original_stem}_{counter:02d}{original_suffix}"
            destination_path = destination_folder / new_name
            counter += 1
        
        # Move the file
        shutil.move(str(source_path), str(destination_path))
        
        return True, destination_path, "Successfully moved"
    
    except Exception as e:
        return False, None, f"Error moving file: {e}"

def bulk_move_by_yaml(source_folder, destination_folder, search_key, search_value):
    """Process all markdown files and move those matching the YAML criteria."""
    source_path = Path(source_folder)
    
    # Find all markdown files in the source folder (top level only)
    md_files = [f for f in source_path.iterdir() if f.is_file() and f.suffix.lower() == '.md']
    
    if not md_files:
        print("No Markdown files found in the source folder.")
        return
    
    print(f"Found {len(md_files)} Markdown files to check.")
    print("-" * 60)
    
    checked_count = 0
    matched_count = 0
    moved_count = 0
    error_count = 0
    
    for md_file in md_files:
        print(f"Checking: {md_file.name}")
        
        # Check if file matches criteria
        is_match, message = check_yaml_value(md_file, search_key, search_value)
        
        if is_match:
            matched_count += 1
            print(f"  ✓ Match found - attempting to move...")
            
            # Move the file
            success, new_path, move_message = move_file_safely(md_file, destination_folder)
            
            if success:
                moved_count += 1
                print(f"  ✓ Moved to: {new_path.name}")
            else:
                error_count += 1
                print(f"  ✗ Move failed: {move_message}")
        else:
            print(f"  - {message}")
        
        checked_count += 1
    
    print("-" * 60)
    print(f"Summary:")
    print(f"  Files checked: {checked_count}")
    print(f"  Files matching criteria: {matched_count}")
    print(f"  Files successfully moved: {moved_count}")
    print(f"  Errors: {error_count}")
    
    if moved_count > 0:
        print(f"\nFiles moved from:")
        print(f"  {source_folder}")
        print(f"To:")
        print(f"  {destination_folder}")

def main():
    print("Bulk Move Markdown Files by YAML Criteria")
    print("=" * 60)
    print("This script moves .md files based on YAML front matter values")
    if YAML_AVAILABLE:
        print("PyYAML detected: Supports string values, lists, and other data types")
    else:
        print("WARNING: PyYAML not installed - using basic mode")
        print("Only simple key: value pairs supported")
        print("Install PyYAML with: pip install PyYAML")
    print("=" * 60)
    
    # Browse for source folder
    print("\nStep 1: Select SOURCE folder (containing .md files to move)")
    source_folder = browse_for_folder("Select SOURCE folder containing Markdown files")
    
    if not source_folder:
        print("No source folder selected. Exiting.")
        return
    
    print(f"Source folder: {source_folder}")
    
    # Browse for destination folder
    print("\nStep 2: Select DESTINATION folder (where files will be moved)")
    destination_folder = browse_for_folder("Select DESTINATION folder for moved files")
    
    if not destination_folder:
        print("No destination folder selected. Exiting.")
        return
    
    print(f"Destination folder: {destination_folder}")
    
    # Validate folders are different
    if Path(source_folder).resolve() == Path(destination_folder).resolve():
        print("ERROR: Source and destination folders cannot be the same!")
        return
    
    # Get search parameters
    print("\nStep 3: Enter search criteria")
    print("Enter the YAML key to search for:")
    search_key = input("YAML key: ").strip()
    
    if not search_key:
        print("No key entered. Exiting.")
        return
    
    print(f"\nEnter the value to search for in '{search_key}':")
    search_value = input("Value to find: ").strip()
    
    if not search_value:
        print("No search value entered. Exiting.")
        return
    
    print(f"\nWill move files where '{search_key}' = '{search_value}'")
    print(f"From: {source_folder}")
    print(f"To: {destination_folder}")
    
    # Confirm before proceeding
    response = input("\nProceed with moving files? (y/N): ").strip().lower()
    if response != 'y':
        print("Operation cancelled.")
        return
    
    # Process the files
    bulk_move_by_yaml(source_folder, destination_folder, search_key, search_value)
    
    print("\nOperation completed!")

if __name__ == "__main__":
    main()
