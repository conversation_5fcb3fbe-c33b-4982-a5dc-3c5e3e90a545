#!/usr/bin/env python3
"""
YouTube Transcript Manager - One-Click Solution
Manages a folder of YouTube video transcripts with automatic processing.
"""

import sys
import logging
import shutil
import random
from pathlib import Path
from tkinter import filedialog, Tk
from typing import List, Optional, Dict, Any
from datetime import datetime

try:
    import whisper
    import yt_dlp
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install: pip install openai-whisper yt-dlp")
    sys.exit(1)

# --- Configuration ---
# Hardcoded folder path - set this to your preferred folder or leave empty for dialog
HARDCODED_FOLDER_PATH = r"C:\Users\<USER>\Documents\Video Transcripts MD"  # Example: "C:/Users/<USER>/Videos/Transcripts"

# File extensions
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.wmv', '.flv']
AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac']
MEDIA_EXTENSIONS = set(VIDEO_EXTENSIONS + AUDIO_EXTENSIONS)

# Transcription settings
TRANSCRIPT_FORMAT = 'md'
CHUNK_SECONDS = 30
WHISPER_MODEL = "large-v3"
USE_FP16 = False

# LLM settings removed - title generation handled by separate script

# File names
INPUT_URL_FILENAME = "InputURL.md"

# --- Setup Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def check_dependencies() -> bool:
    """Check if required dependencies are available."""
    missing = []
    
    if shutil.which("ffmpeg") is None:
        missing.append("ffmpeg")
    if shutil.which("ffprobe") is None:
        missing.append("ffprobe")
    
    if missing:
        logging.error(f"Missing dependencies: {', '.join(missing)}")
        logging.error("Please install ffmpeg and ensure it's in your system's PATH.")
        return False
    
    logging.info("All dependencies found.")
    return True

def choose_folder() -> Optional[Path]:
    """Opens a dialog to choose a folder or uses hardcoded path."""
    if HARDCODED_FOLDER_PATH and Path(HARDCODED_FOLDER_PATH).exists():
        logging.info(f"Using hardcoded folder: {HARDCODED_FOLDER_PATH}")
        return Path(HARDCODED_FOLDER_PATH)
    
    root = Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title="Select Folder Containing InputURL.txt")
    root.destroy()
    
    if folder_path:
        logging.info(f"Selected folder: {folder_path}")
        return Path(folder_path)
    else:
        logging.warning("No folder selected.")
        return None

def find_input_url_file(folder_path: Path) -> Optional[Path]:
    """Find the InputURL.txt file in the folder."""
    input_file = folder_path / INPUT_URL_FILENAME
    if input_file.exists():
        logging.info(f"Found InputURL file: {input_file}")
        return input_file
    else:
        logging.warning(f"InputURL file not found: {input_file}")
        return None

def read_urls_from_file(input_file: Path) -> List[str]:
    """Read URLs from the InputURL file."""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]
        logging.info(f"Found {len(urls)} URLs in InputURL file")
        return urls
    except Exception as e:
        logging.error(f"Error reading InputURL file: {e}")
        return []

def extract_video_metadata(url: str) -> Dict[str, Any]:
    """Extract video metadata using yt-dlp."""
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'extract_flat': False,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            # Extract relevant metadata
            metadata = {
                'title': info.get('title', 'Unknown Title'),
                'uploader': info.get('uploader', 'Unknown Author'),
                'upload_date': info.get('upload_date', ''),
                'url': url,
                'duration': info.get('duration', 0)
            }

            # Check for additional time-related fields
            timestamp = info.get('timestamp')  # Unix timestamp
            upload_time = info.get('upload_time')  # Time string if available

            # Format upload date with time
            formatted_date = format_upload_date_with_time(
                metadata['upload_date'],
                timestamp,
                upload_time
            )
            metadata['formatted_date'] = formatted_date

            # Log available time fields for debugging
            logging.debug(f"Available time fields - upload_date: {metadata['upload_date']}, "
                         f"timestamp: {timestamp}, upload_time: {upload_time}")

            return metadata
    except Exception as e:
        logging.error(f"Error extracting metadata for {url}: {e}")
        return {
            'title': 'Unknown Title',
            'uploader': 'Unknown Author',
            'upload_date': '',
            'formatted_date': '',
            'url': url,
            'duration': 0
        }

def format_upload_date_with_time(upload_date: str, timestamp: Optional[float], upload_time: Optional[str]) -> str:
    """
    Format upload date to YYYYMMDDHHmm if time is available,
    otherwise YYYYMMDDXXXX where XXXX is random number 0000-2359.
    """
    if not upload_date:
        return ''

    try:
        # Start with the base date (YYYYMMDD format from yt-dlp)
        if len(upload_date) >= 8:
            base_date = upload_date[:8]  # YYYYMMDD
        else:
            return upload_date  # Return as-is if format is unexpected

        # Try to extract hour and minute from various sources
        hour = None
        minute = None

        # Method 1: Try timestamp (Unix timestamp)
        if timestamp:
            try:
                dt = datetime.fromtimestamp(timestamp)
                hour = dt.hour
                minute = dt.minute
                logging.debug(f"Extracted time from timestamp: {hour:02d}:{minute:02d}")
            except Exception as e:
                logging.debug(f"Failed to parse timestamp {timestamp}: {e}")

        # Method 2: Try upload_time string
        if hour is None and upload_time:
            try:
                # upload_time might be in format like "14:30" or "14:30:45"
                time_parts = upload_time.split(':')
                if len(time_parts) >= 2:
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
                    logging.debug(f"Extracted time from upload_time: {hour:02d}:{minute:02d}")
            except Exception as e:
                logging.debug(f"Failed to parse upload_time {upload_time}: {e}")

        # If we have hour and minute, use them
        if hour is not None and minute is not None:
            # Validate hour and minute ranges
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                formatted_time = f"{hour:02d}{minute:02d}"
                result = f"{base_date}{formatted_time}"
                logging.info(f"Formatted date with actual time: {result}")
                return result

        # If no time available, generate random time (0000-2359)
        random_hour = random.randint(0, 23)
        random_minute = random.randint(0, 59)
        random_time = f"{random_hour:02d}{random_minute:02d}"
        result = f"{base_date}{random_time}"
        logging.info(f"Formatted date with random time: {result}")
        return result

    except Exception as e:
        logging.warning(f"Error formatting upload date {upload_date}: {e}")
        return upload_date  # Return original if formatting fails

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 200:
        filename = filename[:200]
    
    return filename.strip()

def transcript_exists(folder_path: Path, video_title: str) -> bool:
    """Check if a transcript already exists for the video."""
    safe_title = sanitize_filename(video_title)
    transcript_file = folder_path / f"{safe_title}.{TRANSCRIPT_FORMAT}"
    return transcript_file.exists()

def download_youtube_audio(url: str, output_dir: Path) -> Optional[Path]:
    """Download audio from YouTube URL."""
    ydl_opts = {
        'format': 'm4a/bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',
        }],
        'outtmpl': str(output_dir / '%(title)s.%(ext)s'),
        'noplaylist': True,
        'quiet': False,  # Enable output to see what's happening
    }

    try:
        # Get list of files before download
        files_before = set(output_dir.glob('*'))

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Extract info first to get the filename
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'Unknown')
            safe_title = sanitize_filename(title)

            logging.info(f"Downloading audio for: {title}")

            # Download the audio
            ydl.download([url])

            # Get list of files after download
            files_after = set(output_dir.glob('*'))
            new_files = files_after - files_before

            # Find the newly downloaded audio file
            for new_file in new_files:
                if new_file.suffix.lower() in AUDIO_EXTENSIONS:
                    logging.info(f"Downloaded audio file: {new_file.name}")
                    return new_file

            # Fallback: look for files with the expected title
            for ext in ['.m4a', '.mp3', '.wav', '.aac', '.ogg']:
                # Try exact title match
                audio_file = output_dir / f"{title}{ext}"
                if audio_file.exists():
                    logging.info(f"Found audio file: {audio_file.name}")
                    return audio_file

                # Try sanitized title match
                audio_file = output_dir / f"{safe_title}{ext}"
                if audio_file.exists():
                    logging.info(f"Found audio file: {audio_file.name}")
                    return audio_file

            # Final fallback: look for any audio file with similar name
            for file in output_dir.glob(f"*{safe_title[:20]}*"):
                if file.suffix.lower() in AUDIO_EXTENSIONS:
                    logging.info(f"Found similar audio file: {file.name}")
                    return file

        logging.error(f"Downloaded audio file not found for: {url}")
        logging.info(f"Files in directory: {[f.name for f in output_dir.glob('*')]}")
        return None

    except Exception as e:
        logging.error(f"Error downloading audio from {url}: {e}")
        return None

def format_time(seconds: float) -> str:
    """Convert seconds to HH:MM:SS format."""
    try:
        if seconds < 0:
            seconds = 0
        
        integer_seconds = int(seconds)
        hours, remainder = divmod(integer_seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        return f'{hours:02}:{minutes:02}:{secs:02}'
    except:
        return '00:00:00'

def create_timestamp_url(base_url: str, seconds: float) -> str:
    """Create a timestamped URL for the video."""
    try:
        timestamp = int(seconds)
        if '&' in base_url:
            return f"{base_url}&t={timestamp}"
        else:
            return f"{base_url}&t={timestamp}"
    except:
        return base_url

# Note: Audio stream extraction functions removed since we're working with direct audio downloads
# Note: LLM title generation functions removed - handled by separate generate_chunk_titles.py script

def transcribe_audio_file(audio_path: Path, metadata: Dict[str, Any], model) -> Optional[Path]:
    """Transcribe audio file and create markdown transcript."""
    logging.info(f"Transcribing: {audio_path.name}")

    try:
        # Check if audio file exists and has content
        if not audio_path.exists():
            logging.error(f"Audio file does not exist: {audio_path}")
            return None

        if audio_path.stat().st_size == 0:
            logging.error(f"Audio file is empty: {audio_path}")
            return None

        logging.info(f"Audio file size: {audio_path.stat().st_size} bytes")

        # Transcribe with Whisper
        logging.info("Starting Whisper transcription...")
        result = model.transcribe(str(audio_path), fp16=USE_FP16)
        logging.info("Whisper transcription completed")

        segments = result.get('segments', [])

        if not segments:
            logging.warning(f"No segments found in transcription for {audio_path.name}")
            # Still create a transcript file with metadata even if no speech detected
            segments = []

        # Create output filename
        safe_title = sanitize_filename(metadata['title'])
        output_filename = f"{safe_title}.{TRANSCRIPT_FORMAT}"
        output_path = audio_path.parent / output_filename

        logging.info(f"Writing transcript to: {output_path}")

        with open(output_path, 'w', encoding='utf-8') as f:
            # Write YAML front matter
            f.write("---\n")
            f.write(f"date: {metadata['formatted_date']}\n")
            f.write(f"title: {metadata['title']}\n")
            f.write(f"author: {metadata['uploader']}\n")
            f.write(f"url: {metadata['url']}\n")
            f.write("---\n\n")

            if not segments:
                # No speech detected - create a placeholder entry
                logging.info("No speech segments detected, creating placeholder entry")
                timestamp_url = create_timestamp_url(metadata['url'], 0)
                f.write(f"- [00:00:00]({timestamp_url}) - [No Content] - No speech detected or transcribed\n\n")
            else:
                logging.info(f"Processing {len(segments)} speech segments")

                # Determine total duration for chunking
                max_segment_end_time = 0
                if segments:
                    valid_end_times = [s.get('end') for s in segments if s.get('end') is not None]
                    if valid_end_times:
                        max_segment_end_time = max(valid_end_times)
                    else:
                        valid_start_times = [s.get('start') for s in segments if s.get('start') is not None]
                        if valid_start_times:
                            max_segment_end_time = max(valid_start_times)

                # Ensure we have at least one chunk
                if max_segment_end_time == 0:
                    max_segment_end_time = CHUNK_SECONDS

                num_total_chunks = (int(max_segment_end_time) // CHUNK_SECONDS) + 1
                logging.info(f"Creating {num_total_chunks} chunks of {CHUNK_SECONDS} seconds each")

                for i in range(num_total_chunks):
                    chunk_start_seconds = float(i * CHUNK_SECONDS)
                    chunk_end_seconds = float((i + 1) * CHUNK_SECONDS)

                    # Define extended window for transcription (5 seconds padding)
                    extended_start = max(0, chunk_start_seconds - 5.0)
                    extended_end = chunk_end_seconds + 5.0

                    current_chunk_texts = []

                    for segment in segments:
                        seg_start = segment.get('start')
                        seg_end = segment.get('end')
                        text = segment.get('text', '').strip()

                        if seg_start is not None and seg_end is not None and text:
                            # Include segments that overlap with the extended window
                            if ((extended_start <= seg_start < extended_end) or
                                (extended_start <= seg_end < extended_end) or
                                (seg_start <= extended_start and seg_end >= extended_end)):
                                current_chunk_texts.append(text)

                    full_text_for_chunk = " ".join(current_chunk_texts).strip()

                    if full_text_for_chunk:  # Only write entries with content
                        # Format timestamp
                        timestamp = format_time(chunk_start_seconds)
                        timestamp_url = create_timestamp_url(metadata['url'], chunk_start_seconds)

                        # Use default title - titles will be generated by separate script
                        chunk_title = "Untitled Segment"

                        # Write markdown entry
                        f.write(f"- [{timestamp}]({timestamp_url}) - [{chunk_title}] - {full_text_for_chunk}\n\n")

        logging.info(f"Transcript created successfully: {output_path}")
        return output_path

    except Exception as e:
        logging.error(f"Error transcribing {audio_path.name}: {e}")
        return None

def remove_url_from_file(input_file: Path, url_to_remove: str):
    """Remove a specific URL from the InputURL file."""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Filter out the URL to remove
        updated_lines = [line for line in lines if line.strip() != url_to_remove]

        with open(input_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)

        logging.info(f"Removed URL from InputURL file: {url_to_remove}")

    except Exception as e:
        logging.error(f"Error removing URL from file: {e}")

def cleanup_audio_file(audio_path: Path):
    """Delete the audio file after processing."""
    try:
        if audio_path.exists():
            audio_path.unlink()
            logging.info(f"Deleted audio file: {audio_path.name}")
    except Exception as e:
        logging.error(f"Error deleting audio file {audio_path.name}: {e}")

def process_single_url(url: str, folder_path: Path, input_file: Path, model) -> bool:
    """Process a single YouTube URL."""
    logging.info(f"Processing URL: {url}")

    try:
        # Extract video metadata
        metadata = extract_video_metadata(url)
        video_title = metadata['title']

        # Check if transcript already exists
        if transcript_exists(folder_path, video_title):
            logging.info(f"Transcript already exists for: {video_title}")
            remove_url_from_file(input_file, url)
            return True

        # Download audio
        logging.info(f"Downloading audio for: {video_title}")
        audio_path = download_youtube_audio(url, folder_path)

        if not audio_path:
            logging.error(f"Failed to download audio for: {video_title}")
            return False

        # Transcribe audio
        logging.info(f"Transcribing audio for: {video_title}")
        transcript_path = transcribe_audio_file(audio_path, metadata, model)

        if transcript_path:
            # Clean up audio file
            cleanup_audio_file(audio_path)

            # Remove URL from InputURL file
            remove_url_from_file(input_file, url)

            logging.info(f"Successfully processed: {video_title}")
            return True
        else:
            logging.error(f"Failed to transcribe: {video_title}")
            cleanup_audio_file(audio_path)  # Clean up even on failure
            return False

    except Exception as e:
        logging.error(f"Error processing URL {url}: {e}")
        return False

def main():
    """Main function to manage YouTube transcript processing."""
    logging.info("=== YouTube Transcript Manager Started ===")

    # Check dependencies
    if not check_dependencies():
        logging.error("Missing required dependencies. Exiting.")
        return

    # Choose folder
    folder_path = choose_folder()
    if not folder_path:
        logging.error("No folder selected. Exiting.")
        return

    # Find InputURL file
    input_file = find_input_url_file(folder_path)
    if not input_file:
        logging.error(f"InputURL.txt file not found in {folder_path}. Exiting.")
        return

    # Read URLs from file
    urls = read_urls_from_file(input_file)
    if not urls:
        logging.info("No URLs found in InputURL file. Nothing to process.")
        return

    logging.info(f"Found {len(urls)} URLs to process")

    # Note: Title generation will be handled by separate generate_chunk_titles.py script
    logging.info("Transcripts will use default 'Untitled Segment' titles - run generate_chunk_titles.py afterward for AI titles")

    # Load Whisper model
    try:
        logging.info(f"Loading Whisper model: {WHISPER_MODEL}")
        logging.info("This may take some time for larger models...")
        model = whisper.load_model(WHISPER_MODEL)
        logging.info("Whisper model loaded successfully")
    except Exception as e:
        logging.error(f"Failed to load Whisper model: {e}")
        return

    # Process each URL
    processed_count = 0
    failed_count = 0

    logging.info("=== Starting URL Processing ===")

    for i, url in enumerate(urls, 1):
        logging.info(f"Processing {i}/{len(urls)}: {url}")

        try:
            if process_single_url(url, folder_path, input_file, model):
                processed_count += 1
                logging.info(f"✓ Successfully processed URL {i}/{len(urls)}")
            else:
                failed_count += 1
                logging.error(f"✗ Failed to process URL {i}/{len(urls)}")
        except Exception as e:
            failed_count += 1
            logging.error(f"✗ Critical error processing URL {i}/{len(urls)}: {e}")

    # Summary
    logging.info("=== Processing Summary ===")
    logging.info(f"Total URLs found: {len(urls)}")
    logging.info(f"Successfully processed: {processed_count}")
    logging.info(f"Failed: {failed_count}")

    if processed_count > 0:
        logging.info(f"Transcripts saved to: {folder_path}")
        logging.info("💡 To generate AI titles for chunks, run: python generate_chunk_titles.py")

    logging.info("=== YouTube Transcript Manager Finished ===")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        logging.info("Process interrupted by user")
    except Exception as e:
        logging.error(f"Unexpected error: {e}", exc_info=True)
    finally:
        logging.info("Script execution completed")
