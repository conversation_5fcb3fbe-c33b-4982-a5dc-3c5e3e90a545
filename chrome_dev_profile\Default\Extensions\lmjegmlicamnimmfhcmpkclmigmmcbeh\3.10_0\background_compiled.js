/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';function n(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var q="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function aa(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var r=aa(this);function t(a,b){if(b)a:{var c=r;a=a.split(".");for(var e=0;e<a.length-1;e++){var g=a[e];if(!(g in c))break a;c=c[g]}a=a[a.length-1];e=c[a];b=b(e);b!=e&&null!=b&&q(c,a,{configurable:!0,writable:!0,value:b})}}
t("Symbol",function(a){function b(h){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(e+(h||"")+"_"+g++,h)}function c(h,d){this.g=h;q(this,"description",{configurable:!0,writable:!0,value:d})}if(a)return a;c.prototype.toString=function(){return this.g};var e="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",g=0;return b});
t("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var e=r[b[c]];"function"===typeof e&&"function"!=typeof e.prototype[a]&&q(e.prototype,a,{configurable:!0,writable:!0,value:function(){return ba(n(this))}})}return a});function ba(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
function u(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:n(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}function w(){this.j=!1;this.h=null;this.m=void 0;this.g=1;this.o=0;this.i=null}function x(a){if(a.j)throw new TypeError("Generator is already running");a.j=!0}w.prototype.l=function(a){this.m=a};function y(a,b){a.i={F:b,G:!0};a.g=a.o}
w.prototype.return=function(a){this.i={return:a};this.g=this.o};function z(a,b,c){a.g=c;return{value:b}}function ca(a){this.g=new w;this.h=a}function da(a,b){x(a.g);var c=a.g.h;if(c)return A(a,"return"in c?c["return"]:function(e){return{value:e,done:!0}},b,a.g.return);a.g.return(b);return B(a)}
function A(a,b,c,e){try{var g=b.call(a.g.h,c);if(!(g instanceof Object))throw new TypeError("Iterator result "+g+" is not an object");if(!g.done)return a.g.j=!1,g;var h=g.value}catch(d){return a.g.h=null,y(a.g,d),B(a)}a.g.h=null;e.call(a.g,h);return B(a)}function B(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.j=!1,{value:b.value,done:!1}}catch(c){a.g.m=void 0,y(a.g,c)}a.g.j=!1;if(a.g.i){b=a.g.i;a.g.i=null;if(b.G)throw b.F;return{value:b.return,done:!0}}return{value:void 0,done:!0}}
function ea(a){this.next=function(b){x(a.g);a.g.h?b=A(a,a.g.h.next,b,a.g.l):(a.g.l(b),b=B(a));return b};this.throw=function(b){x(a.g);a.g.h?b=A(a,a.g.h["throw"],b,a.g.l):(y(a.g,b),b=B(a));return b};this.return=function(b){return da(a,b)};this[Symbol.iterator]=function(){return this}}function fa(a){function b(e){return a.next(e)}function c(e){return a.throw(e)}return new Promise(function(e,g){function h(d){d.done?e(d.value):Promise.resolve(d.value).then(b,c).then(h,g)}h(a.next())})}
function C(a){return fa(new ea(new ca(a)))}
t("Promise",function(a){function b(d){this.h=0;this.i=void 0;this.g=[];this.o=!1;var f=this.j();try{d(f.resolve,f.reject)}catch(k){f.reject(k)}}function c(){this.g=null}function e(d){return d instanceof b?d:new b(function(f){f(d)})}if(a)return a;c.prototype.h=function(d){if(null==this.g){this.g=[];var f=this;this.i(function(){f.l()})}this.g.push(d)};var g=r.setTimeout;c.prototype.i=function(d){g(d,0)};c.prototype.l=function(){for(;this.g&&this.g.length;){var d=this.g;this.g=[];for(var f=0;f<d.length;++f){var k=
d[f];d[f]=null;try{k()}catch(l){this.j(l)}}}this.g=null};c.prototype.j=function(d){this.i(function(){throw d;})};b.prototype.j=function(){function d(l){return function(m){k||(k=!0,l.call(f,m))}}var f=this,k=!1;return{resolve:d(this.A),reject:d(this.l)}};b.prototype.A=function(d){if(d===this)this.l(new TypeError("A Promise cannot resolve to itself"));else if(d instanceof b)this.C(d);else{a:switch(typeof d){case "object":var f=null!=d;break a;case "function":f=!0;break a;default:f=!1}f?this.v(d):this.m(d)}};
b.prototype.v=function(d){var f=void 0;try{f=d.then}catch(k){this.l(k);return}"function"==typeof f?this.D(f,d):this.m(d)};b.prototype.l=function(d){this.u(2,d)};b.prototype.m=function(d){this.u(1,d)};b.prototype.u=function(d,f){if(0!=this.h)throw Error("Cannot settle("+d+", "+f+"): Promise already settled in state"+this.h);this.h=d;this.i=f;2===this.h&&this.B();this.H()};b.prototype.B=function(){var d=this;g(function(){if(d.I()){var f=r.console;"undefined"!==typeof f&&f.error(d.i)}},1)};b.prototype.I=
function(){if(this.o)return!1;var d=r.CustomEvent,f=r.Event,k=r.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof d?d=new d("unhandledrejection",{cancelable:!0}):"function"===typeof f?d=new f("unhandledrejection",{cancelable:!0}):(d=r.document.createEvent("CustomEvent"),d.initCustomEvent("unhandledrejection",!1,!0,d));d.promise=this;d.reason=this.i;return k(d)};b.prototype.H=function(){if(null!=this.g){for(var d=0;d<this.g.length;++d)h.h(this.g[d]);this.g=null}};var h=new c;b.prototype.C=
function(d){var f=this.j();d.s(f.resolve,f.reject)};b.prototype.D=function(d,f){var k=this.j();try{d.call(f,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(d,f){function k(p,v){return"function"==typeof p?function(K){try{l(p(K))}catch(L){m(L)}}:v}var l,m,M=new b(function(p,v){l=p;m=v});this.s(k(d,l),k(f,m));return M};b.prototype.catch=function(d){return this.then(void 0,d)};b.prototype.s=function(d,f){function k(){switch(l.h){case 1:d(l.i);break;case 2:f(l.i);break;default:throw Error("Unexpected state: "+
l.h);}}var l=this;null==this.g?h.h(k):this.g.push(k);this.o=!0};b.resolve=e;b.reject=function(d){return new b(function(f,k){k(d)})};b.race=function(d){return new b(function(f,k){for(var l=u(d),m=l.next();!m.done;m=l.next())e(m.value).s(f,k)})};b.all=function(d){var f=u(d),k=f.next();return k.done?e([]):new b(function(l,m){function M(K){return function(L){p[K]=L;v--;0==v&&l(p)}}var p=[],v=0;do p.push(void 0),v++,e(k.value).s(M(p.length-1),m),k=f.next();while(!k.done)})};return b});
function ha(a,b){a instanceof String&&(a+="");var c=0,e=!1,g={next:function(){if(!e&&c<a.length){var h=c++;return{value:b(h,a[h]),done:!1}}e=!0;return{done:!0,value:void 0}}};g[Symbol.iterator]=function(){return g};return g}t("Array.prototype.entries",function(a){return a?a:function(){return ha(this,function(b,c){return[b,c]})}});
t("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),e=this,g=!1,h={next:function(){if(g)return{value:void 0,done:!0};var d=c.exec(e);if(!d)return g=!0,{value:void 0,done:!0};""===d[0]&&(c.lastIndex+=1);return{value:d,done:!1}}};h[Symbol.iterator]=function(){return h};return h}});var ia=this||self;
function ja(a,b,c){return a.call.apply(a.bind,arguments)}function ka(a,b,c){if(!a)throw Error();if(2<arguments.length){var e=Array.prototype.slice.call(arguments,2);return function(){var g=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(g,e);return a.apply(b,g)}}return function(){return a.apply(b,arguments)}}function D(a,b,c){D=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?ja:ka;return D.apply(null,arguments)}
function la(a,b){function c(){}c.prototype=b.prototype;a.K=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.J=function(e,g,h){for(var d=Array(arguments.length-2),f=2;f<arguments.length;f++)d[f-2]=arguments[f];return b.prototype[g].apply(e,d)}};function E(a,b){this.name=a;this.value=b}E.prototype.toString=function(){return this.name};var F=new E("OFF",Infinity),G=new E("SEVERE",1E3),ma=new E("WARNING",900),na=new E("INFO",800),H=new E("CONFIG",700),oa=new E("FINE",500);function I(){this.clear()}var pa;I.prototype.clear=function(){};function J(a,b,c){this.g=void 0;this.reset(a||F,b,c,void 0,void 0)}J.prototype.reset=function(a,b,c,e){this.i=e||Date.now();this.j=a;this.l=b;this.h=c;this.g=void 0};J.prototype.getMessage=function(){return this.l};
function N(a,b){this.g=null;this.j=[];this.i=(void 0===b?null:b)||null;this.l=[];this.h={g:function(){return a}}}function qa(a){return a.g?a.g:a.i?qa(a.i):F}N.prototype.publish=function(a){for(var b=this;b;)b.j.forEach(function(c){c(a)}),b=b.i};function ra(){this.entries={};var a=new N("");a.g=H;this.entries[""]=a}var O;function P(a,b){var c=a.entries[b];if(c)return c;c=P(a,b.slice(0,Math.max(b.lastIndexOf("."),0)));var e=new N(b,c);a.entries[b]=e;c.l.push(e);return e}
function Q(){O||(O=new ra);return O}function R(a,b,c){var e;if(e=a)if(e=a&&b){e=b.value;var g=a?qa(P(Q(),a.g())):F;e=e>=g.value}e&&(b=b||F,e=P(Q(),a.g()),"function"===typeof c&&(c=c()),pa||(pa=new I),a=new J(b,c,a.g()),a.g=void 0,e.publish(a))}function S(a,b){a&&R(a,oa,b)};var T=P(Q(),"switchblade.drive.proxy").h;
function sa(a){if(a.name&&"com.google.drive.nativeproxy"===a.name){S(T,"Establishing proxy connection to native host: "+a.name);var b=chrome.runtime.connectNative(a.name);b.onDisconnect.addListener(function(){var c,e=(null==(c=chrome.runtime.lastError)?void 0:c.message)||"";S(T,"Native message port disconnected: "+e);a.disconnect()});b.onMessage.addListener(function(c){S(T,"Native response: "+JSON.stringify(c));a.postMessage(c)});a.onDisconnect.addListener(function(){S(T,"Browser message port disconnected");
b.disconnect()});a.onMessage.addListener(function(c){S(T,"Browser request: "+c);b.postMessage(c)})}else T&&R(T,G,"Bad name passed to native proxy extension."),a.disconnect()};var U=P(Q(),"switchblade.offscreen.proxy").h,ta={url:"offscreen.html",reasons:["IFRAME_SCRIPTING"],justification:"For communication with the Google Docs Offline extension."};function ua(){var a;this.g=a=void 0===a?self:a}
function va(a){var b;return C(function(c){if(1==c.g)return z(c,a.g.clients.matchAll(function(e){return e.url.contains("offscreen.html")}),2);b=c.m;if(void 0!=b&&0<b.length)return c.return();S(U,"Creating new offscreen document.");return z(c,new Promise(function(e){chrome.offscreen.createDocument(ta,e)}),0)})}
function wa(a,b){var c;C(function(e){if(!b.sender||"com.google.drive.nativeproxy"!==b.sender.nativeApplication)return U&&R(U,G,"Bad native name passed to native proxy extension."),b.disconnect(),e.return();c=va(a).then(function(){S(U,"Establishing proxy connection to offscreen document.");var g=chrome.runtime.connect({name:"com.google.drive.offscreenproxy"});g.onDisconnect.addListener(function(){var h,d=(null==(h=chrome.runtime.lastError)?void 0:h.message)||"";S(U,"Offscreen message port disconnected: "+
d);b.disconnect()});g.onMessage.addListener(function(h){S(U,"Offscreen response: "+JSON.stringify(h));b.postMessage(h)});return g});b.onDisconnect.addListener(function(){var g,h;return C(function(d){h=(null==(g=chrome.runtime.lastError)?void 0:g.message)||"";S(U,"Native message port disconnected: "+h);c.then(function(f){return f.disconnect()});d.g=0})});b.onMessage.addListener(function(g){return C(function(h){S(U,"Native request: "+JSON.stringify(g));c.then(function(d){return d.postMessage(g)});h.g=
0})});e.g=0})};function V(){this.g=Date.now()}var W=null;V.prototype.set=function(a){this.g=a};V.prototype.reset=function(){this.set(Date.now())};V.prototype.get=function(){return this.g};function X(a){this.j=a||"";W||(W=new V);this.l=W}X.prototype.g=!0;X.prototype.h=!0;X.prototype.i=!1;function Y(a){return 10>a?"0"+a:String(a)}function xa(a){X.call(this,a)}la(xa,X);
function ya(a,b){var c=[];c.push(a.j," ");if(a.h){var e=new Date(b.i);c.push("[",Y(e.getFullYear()-2E3)+Y(e.getMonth()+1)+Y(e.getDate())+" "+Y(e.getHours())+":"+Y(e.getMinutes())+":"+Y(e.getSeconds())+"."+Y(Math.floor(e.getMilliseconds()/10)),"] ")}e=c.push;var g=a.l.get();g=(b.i-g)/1E3;var h=g.toFixed(3),d=0;if(1>g)d=2;else for(;100>g;)d++,g*=10;for(;0<d--;)h=" "+h;e.call(c,"[",h,"s] ");c.push("[",b.h,"] ");c.push(b.getMessage());a.i&&(b=b.g,void 0!==b&&c.push("\n",b instanceof Error?b.message:String(b)));
a.g&&c.push("\n");return c.join("")};function za(){this.l=D(this.i,this);this.g=new xa;this.g.h=!1;this.g.i=!1;this.h=this.g.g=!1;this.j={}}za.prototype.i=function(a){function b(h){if(h){if(h.value>=G.value)return"error";if(h.value>=ma.value)return"warn";if(h.value>=H.value)return"log"}return"debug"}if(!this.j[a.h]){var c=ya(this.g,a),e=Aa;if(e){var g=b(a.j);Ba(e,g,c,a.g)}}};var Aa=ia.console;function Ba(a,b,c,e){if(a[b])a[b](c,void 0===e?"":e);else a.log(c,void 0===e?"":e)};var Ca=P(Q(),"switchblade").h;Ca&&(P(Q(),Ca.g()).g=na);var Z=new za;if(1!=Z.h){var Da=P(Q(),"").h,Ea=Z.l;Da&&P(Q(),Da.g()).j.push(Ea);Z.h=!0}(function(){var a=new ua;chrome.runtime.onConnectNative&&chrome.runtime.onConnectNative.addListener(function(b){wa(a,b)})})();chrome.runtime.onConnectExternal&&chrome.runtime.onConnectExternal.addListener(function(a){sa(a)});
