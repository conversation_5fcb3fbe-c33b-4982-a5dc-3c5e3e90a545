import re
from datetime import datetime
from tkinter import Tk
from tkinter.filedialog import askopenfilename, asksaveas<PERSON>lena<PERSON>

def reformat_transaction(input_text):
    transactions = []

    # Split the input text into lines
    lines = input_text.strip().split('\n')

    for line in lines:
        # Skip header or any lines that don't match the expected format
        if not re.match(r'\d{2}/\d{2}/\d{4}', line):
            continue

        # Split each line by commas and handle potential embedded commas in description
        parts = [p.strip() for p in re.split(r',(?=(?:[^"]*"[^"]*")*[^"]*$)', line)]

        date_str = parts[0]
        description_str = parts[4]
        amount_str = parts[5]

        # Reformat date
        date_obj = datetime.strptime(date_str, "%m/%d/%Y")
        formatted_month = date_obj.strftime("%b").upper()
        formatted_day = str(date_obj.day)

        # Clean up description and amount
        description_str = ' '.join(description_str.strip().split())
        amount_str = amount_str.replace('(', '-').replace(')', '').replace('$', '').replace(',', '').strip()

        # Format output for this transaction
        output = f"{formatted_month}\t{formatted_day}\tST\t{description_str}\t\t{amount_str}"
        transactions.append(output)

    # Join all formatted transactions into a single output text with line breaks
    return '\n'.join(transactions)

def process_transactions():
    try:
        # Use tkinter to ask the user for the input file path
        Tk().withdraw()  # Hide the root window
        input_file_path = askopenfilename(title="Select the input text file", filetypes=[("Text files", "*.txt")])
        
        if not input_file_path:
            print("No file selected. Exiting.")
            return

        with open(input_file_path, 'r') as file:
            input_text = file.read()
        
        output_text = reformat_transaction(input_text)

        # Use tkinter to ask the user for the output file path
        output_file_path = asksaveasfilename(title="Save output as", defaultextension=".txt", filetypes=[("Text files", "*.txt")])

        if not output_file_path:
            print("No output file selected. Exiting.")
            return

        with open(output_file_path, 'w') as file:
            file.write(output_text + '\n')

        print(f"Output written successfully to {output_file_path}")

    except Exception as e:
        print(f"An error occurred: {e}")

# Run the process_transactions function
process_transactions()