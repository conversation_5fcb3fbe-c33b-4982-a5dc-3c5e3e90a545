import re
from bs4 import BeautifulSoup
from datetime import datetime, timed<PERSON><PERSON>

def extract_reddit_posts(html_content):
    soup = BeautifulSoup(html_content, 'lxml')
    posts = soup.find_all('div', attrs={'data-testid': 'search-post-unit'})
    
    extracted_posts = []
    
    for post in posts:
        title_anchor = post.find('a', attrs={'data-testid': 'post-title-text'})
        if not title_anchor:
            title_anchor = post.find('a', attrs={'data-testid': 'post-title'})

        if not title_anchor:
            continue

        post_title = title_anchor.get_text(strip=True)
        post_url_slug = title_anchor.get('href')

        post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
        if not post_url_match:
            continue
        
        post_url = f"https://www.reddit.com{post_url_match.group(1)}"
        
        time_tag = post.find('time')
        if not time_tag or not time_tag.has_attr('datetime'):
            continue
            
        created_timestamp_str = time_tag['datetime']
        
        utc_time = datetime.fromisoformat(created_timestamp_str.replace('Z', '+00:00'))
        
        edt_time = utc_time - timedelta(hours=4)
        
        formatted_time = edt_time.strftime('%Y%m%d%H%M')
        
        extracted_posts.append(f"- {formatted_time} - [{post_title}]({post_url})")
        
    return extracted_posts

import pyperclip

if __name__ == "__main__":
    try:
        # with open('reddit_search_result.html', 'r', encoding='utf-8') as f:
        #     html_content = f.read()
        html_content = pyperclip.paste()
        
        if not html_content:
            print("File is empty.")
        else:
            posts = extract_reddit_posts(html_content)
            
            output = "\n".join(posts)
            pyperclip.copy(output)
            print(output)
            print("\nResults copied to clipboard.")
            
    except Exception as e:
        print(f"An error occurred: {e}")
