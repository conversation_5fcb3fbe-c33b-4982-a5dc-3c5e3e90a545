#!/usr/bin/env python3
"""
Simplified YouTube Transcriber - Debug Version
Focus on core functionality to identify issues.
"""

import logging
import random
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

try:
    import whisper
    import yt_dlp
except ImportError as e:
    print(f"Missing dependency: {e}")
    print("Install with: pip install openai-whisper yt-dlp")
    exit(1)

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename[:200].strip()

def extract_video_metadata(url: str) -> Dict[str, Any]:
    """Extract video metadata using yt-dlp."""
    ydl_opts = {'quiet': True, 'no_warnings': True}

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            metadata = {
                'title': info.get('title', 'Unknown Title'),
                'uploader': info.get('uploader', 'Unknown Author'),
                'upload_date': info.get('upload_date', ''),
                'url': url,
            }

            # Check for additional time-related fields
            timestamp = info.get('timestamp')
            upload_time = info.get('upload_time')

            # Format upload date with time
            metadata['formatted_date'] = format_upload_date_with_time(
                metadata['upload_date'],
                timestamp,
                upload_time
            )

            return metadata
    except Exception as e:
        logging.error(f"Error extracting metadata: {e}")
        return {
            'title': 'Unknown Title',
            'uploader': 'Unknown Author',
            'upload_date': '',
            'formatted_date': '',
            'url': url,
        }

def format_upload_date_with_time(upload_date: str, timestamp: Optional[float], upload_time: Optional[str]) -> str:
    """Format upload date to YYYYMMDDHHmm if time available, otherwise YYYYMMDDXXXX with random time."""
    if not upload_date:
        return ''

    try:
        # Start with the base date (YYYYMMDD format from yt-dlp)
        if len(upload_date) >= 8:
            base_date = upload_date[:8]  # YYYYMMDD
        else:
            return upload_date

        # Try to extract hour and minute
        hour = None
        minute = None

        # Method 1: Try timestamp (Unix timestamp)
        if timestamp:
            try:
                dt = datetime.fromtimestamp(timestamp)
                hour = dt.hour
                minute = dt.minute
            except:
                pass

        # Method 2: Try upload_time string
        if hour is None and upload_time:
            try:
                time_parts = upload_time.split(':')
                if len(time_parts) >= 2:
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
            except:
                pass

        # Use actual time if available, otherwise random
        if hour is not None and minute is not None and 0 <= hour <= 23 and 0 <= minute <= 59:
            formatted_time = f"{hour:02d}{minute:02d}"
        else:
            # Generate random time (0000-2359)
            random_hour = random.randint(0, 23)
            random_minute = random.randint(0, 59)
            formatted_time = f"{random_hour:02d}{random_minute:02d}"

        return f"{base_date}{formatted_time}"

    except Exception as e:
        logging.warning(f"Error formatting upload date {upload_date}: {e}")
        return upload_date

def download_youtube_audio(url: str, output_dir: Path) -> Optional[Path]:
    """Download audio from YouTube URL."""
    ydl_opts = {
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',
        }],
        'outtmpl': str(output_dir / '%(title)s.%(ext)s'),
        'noplaylist': True,
    }
    
    try:
        # Get files before download
        files_before = set(output_dir.glob('*'))
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            logging.info("Downloading audio...")
            ydl.download([url])
        
        # Find new files
        files_after = set(output_dir.glob('*'))
        new_files = files_after - files_before
        
        # Find audio file
        audio_extensions = {'.m4a', '.mp3', '.wav', '.aac', '.ogg'}
        for new_file in new_files:
            if new_file.suffix.lower() in audio_extensions:
                logging.info(f"Downloaded: {new_file.name}")
                return new_file
        
        logging.error("No audio file found after download")
        return None
        
    except Exception as e:
        logging.error(f"Download error: {e}")
        return None

def transcribe_audio(audio_path: Path, metadata: Dict[str, Any], model) -> Optional[Path]:
    """Transcribe audio file."""
    logging.info(f"Transcribing: {audio_path.name}")
    
    try:
        # Check file
        if not audio_path.exists():
            logging.error("Audio file does not exist")
            return None
        
        file_size = audio_path.stat().st_size
        logging.info(f"File size: {file_size} bytes")
        
        if file_size == 0:
            logging.error("Audio file is empty")
            return None
        
        # Transcribe
        logging.info("Starting Whisper transcription...")
        result = model.transcribe(str(audio_path))
        logging.info("Transcription completed")
        
        # Get segments
        segments = result.get('segments', [])
        text = result.get('text', '').strip()
        
        logging.info(f"Found {len(segments)} segments")
        logging.info(f"Total text length: {len(text)} characters")
        
        if text:
            logging.info(f"Sample text: {text[:100]}...")
        
        # Create output file
        safe_title = sanitize_filename(metadata['title'])
        output_path = audio_path.parent / f"{safe_title}.md"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            # YAML front matter
            f.write("---\n")
            f.write(f"date: {metadata['formatted_date']}\n")
            f.write(f"title: {metadata['title']}\n")
            f.write(f"author: {metadata['uploader']}\n")
            f.write(f"url: {metadata['url']}\n")
            f.write("---\n\n")
            
            if not segments:
                f.write("- [00:00:00]() - [No Content] - No speech detected\n\n")
            else:
                # Simple approach: write all text as one chunk
                f.write(f"- [00:00:00]({metadata['url']}) - [Full Transcript] - {text}\n\n")
        
        logging.info(f"Transcript saved: {output_path}")
        return output_path
        
    except Exception as e:
        logging.error(f"Transcription error: {e}")
        return None

def process_url(url: str, output_dir: Path) -> bool:
    """Process a single YouTube URL."""
    logging.info(f"Processing: {url}")
    
    try:
        # Load Whisper model
        logging.info("Loading Whisper model...")
        model = whisper.load_model("base")  # Use smaller model for testing
        logging.info("Model loaded")
        
        # Get metadata
        metadata = extract_video_metadata(url)
        logging.info(f"Video: {metadata['title']}")
        
        # Download audio
        audio_path = download_youtube_audio(url, output_dir)
        if not audio_path:
            return False
        
        # Transcribe
        transcript_path = transcribe_audio(audio_path, metadata, model)
        
        # Cleanup
        if audio_path.exists():
            audio_path.unlink()
            logging.info("Audio file deleted")
        
        return transcript_path is not None
        
    except Exception as e:
        logging.error(f"Processing error: {e}")
        return False

def main():
    """Main function."""
    logging.info("=== Simple YouTube Transcriber ===")
    
    # Get URL
    url = input("Enter YouTube URL: ").strip()
    if not url:
        logging.error("No URL provided")
        return
    
    # Get output directory
    output_dir = Path.cwd()
    logging.info(f"Output directory: {output_dir}")
    
    # Process
    success = process_url(url, output_dir)
    
    if success:
        logging.info("✓ Processing completed successfully")
    else:
        logging.error("✗ Processing failed")

if __name__ == '__main__':
    main()
